GIT
  remote: https://github.com/SimonBo/capistrano-db-tasks.git
  revision: 4d65064b2f3a78016249139929e8e48a0d68c965
  specs:
    capistrano-db-tasks (0.6)
      capistrano (>= 3.0.0)

GIT
  remote: https://github.com/SimonBo/select2-rails.git
  revision: a63be7b2d7d9acf3685a00326dc57fea2c30e1ae
  branch: rails_7_431
  specs:
    select2-rails (3.4.1)
      sass-rails
      thor

GIT
  remote: https://github.com/andresprogra/rfc822.git
  revision: 7dffcaf20a834ff4485948b0ab85a95b9219e124
  specs:
    rfc822 (0.1.6)

GIT
  remote: https://github.com/mimemagicrb/mimemagic.git
  revision: 01f92d86d15d85cfd0f20dabd025dcbd36a8a60f
  ref: 01f92d86d15d85cfd0f20dabd025dcbd36a8a60f
  specs:
    mimemagic (0.3.5)

GEM
  remote: http://rails-assets.org/
  specs:

GEM
  remote: https://rubygems.org/
  remote: http://rails-assets.org/
  specs:
    Ascii85 (2.0.1)
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_admin_sidebar (2.0.0)
      activeadmin
    active_model_otp (2.3.4)
      activemodel
      rotp (~> 6.3.0)
    active_record_query_trace (1.8.3)
      activerecord (>= 6.0.0)
    activeadmin (3.2.5)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    acts_as_list (1.2.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    acts_as_xlsx (1.0.6)
      activerecord (>= 2.3.9)
      axlsx (>= 1.0.13)
      i18n (>= 0.4.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    afm (0.2.2)
    airbrussh (1.5.3)
      sshkit (>= 1.6.1, != 1.7.0)
    akami (1.3.3)
      base64
      gyoku (>= 0.4.0)
      nokogiri
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.3)
    autoprefixer-rails (*********)
      execjs (~> 2)
    avatarly (1.6.1)
      rfc822
      rmagick
      unicode_utils
    awesome_print (1.9.2)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    axlsx (1.3.6)
      htmlentities (~> 4.3.1)
      nokogiri (>= 1.4.1)
      rubyzip (>= 0.9.5)
    axlsx_styler (1.2.0)
      activesupport (>= 3.1)
      caxlsx (>= 2.0.2, < 3.3.0)
    base64 (0.3.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    bootstrap-sass (3.4.1)
      autoprefixer-rails (>= 5.2.1)
      sassc (>= 2.0.0)
    brakeman (7.0.2)
      racc
    browser (2.7.1)
    browsernizer (0.2.4)
      browser (>= 2.0, < 3.0)
    builder (3.3.0)
    bullet (8.0.0)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    cancancan (3.6.1)
    capistrano (3.19.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.1)
      capistrano (~> 3.1)
    capistrano-rails (1.7.0)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-email (3.0.2)
      capybara (>= 2.4, < 4.0)
      mail
    capybara-screenshot (1.0.26)
      capybara (>= 1.0, < 4)
      launchy
    capybara-select2 (1.0.1)
      capybara
      rspec
    carrierwave (3.1.2)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    carrierwave-base64 (2.11.0)
      carrierwave (>= 2.2.1)
      marcel (~> 1.0.0)
      mime-types (~> 3.0)
    caxlsx (3.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.4)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    certified (1.0.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    chronic (0.10.2)
    chunky_png (1.4.0)
    cliver (0.3.2)
    cmxl (2.0)
      rchardet
    cocoon (1.2.15)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    combine_pdf (1.0.31)
      matrix
      ruby-rc4 (>= 0.1.5)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.2)
    d3-rails (7.8.5)
      railties (>= 3.1)
    daemons (1.4.1)
    data-confirm-modal (1.6.3)
      railties (>= 3.0)
    database_cleaner (2.1.0)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    decent_exposure (3.0.4)
      activesupport (>= 4.0)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    descriptive-statistics (2.2.0)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-security (0.18.0)
      devise (>= 4.3.0)
    diff-lcs (1.5.1)
    dkim (1.1.0)
    docile (1.4.1)
    drb (2.2.3)
    ed25519 (1.3.0)
    email_spec (2.3.0)
      htmlentities (~> 4.3.3)
      launchy (>= 2.1, < 4.0)
      mail (~> 2.7)
    email_validator (2.2.4)
      activemodel
    enumerize (2.8.1)
      activesupport (>= 3.2)
    erb (5.0.1)
    erubi (1.13.1)
    exception_notification (5.0.0)
      actionmailer (>= 7.1, < 9)
      activesupport (>= 7.1, < 9)
    execjs (2.10.0)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffi (1.17.2)
    font-awesome-rails (4.7.0.9)
      railties (>= 3.2, < 9.0)
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    gon (6.4.0)
      actionpack (>= 3.0.20)
      i18n (>= 0.7)
      multi_json
      request_store (>= 1.0)
    groupdate (6.7.0)
      activesupport (>= 7.1)
    gruff (0.6.0)
      rmagick (>= 2.13.4)
    gyoku (1.4.0)
      builder (>= 2.1.2)
      rexml (~> 3.0)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.2.0)
    hashery (2.1.2)
    high_voltage (4.0.0)
    htmlentities (4.3.4)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpi (4.0.4)
      base64
      mutex_m
      nkf
      rack (>= 2.0, < 4)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    icalendar (2.10.3)
      ice_cube (~> 0.16)
      ostruct
    ice_cube (0.17.0)
    ice_nine (0.11.2)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.8.0)
    irb (1.14.3)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-timepicker-addon-rails (1.4.1)
      railties (>= 3.1)
    jquery-ui-rails (7.0.0)
      railties (>= 3.2.16)
    json (2.9.1)
    just-datetime-picker (0.0.7)
      activeadmin (>= 0.4.4)
      formtastic (>= 2.0.0)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kgio (2.11.4)
    language_server-protocol (********)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    locales_export_import (0.5.0)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    meta-tags (2.22.1)
      actionpack (>= 6.0.0, < 8.1)
    method_source (1.1.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0107)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    minitest-focus (1.4.0)
      minitest (>= 4, < 6)
    minitest-stub-const (0.6)
    mocha (2.7.1)
      ruby2_keywords (>= 0.0.5)
    msgpack (1.7.5)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.3.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nkf (0.2.0)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nori (2.7.1)
      bigdecimal
    observer (0.1.2)
    oj (3.16.9)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.26.3)
    parallel_tests (4.9.0)
      parallel
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    paranoia_uniqueness_validator (3.7.0)
      activerecord (>= 7.0, < 8.1)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pdf-core (0.10.0)
    pdf-inspector (1.3.0)
      pdf-reader (>= 1.0, < 3.0.a)
    pdf-reader (2.14.1)
      Ascii85 (>= 1.0, < 3.0, != 2.0.0)
      afm (~> 0.2.1)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pg (1.5.9)
    phantomjs (2.1.1.0)
    pkg-config (1.5.9)
    pluck_all (2.3.4)
      activesupport (>= 3.0.0)
      rails_compatibility (>= 0.0.10)
    pluck_to_hash (1.0.2)
      activerecord (>= 4.0.2)
      activesupport (>= 4.0.2)
    poltergeist (1.18.1)
      capybara (>= 2.1, < 4)
      cliver (~> 0.3.1)
      websocket-driver (>= 0.2.0)
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-markup (1.0.1)
      nokogiri
      prawn
      prawn-table
    prawn-table (0.2.2)
      prawn (>= 1.3.0, < 3.0.0)
    prawn-templates (0.1.2)
      pdf-reader (~> 2.0)
      prawn (~> 2.2)
    prism (1.4.0)
    psych (5.2.2)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.5.0)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-assets-bootbox (4.4.0)
      rails-assets-bootstrap (>= 3.0.0)
    rails-assets-bootstrap (5.3.3)
    rails-assets-jquery (3.7.1)
    rails-assets-remarkable-bootstrap-notify (3.1.3)
      rails-assets-bootstrap (>= 2.0.0)
      rails-assets-jquery (>= 1.10.2)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-patch-json-encode (0.2.0)
      multi_json (~> 1.0, >= 1.9.3)
    rails_compatibility (0.0.10)
      activerecord (>= 3)
    rails_performance (1.4.1)
      browser
      railties
      redis
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    raindrops (0.20.1)
    rake (13.2.1)
    ransack (4.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rchardet (1.9.0)
    rdoc (6.14.0)
      erb
      psych (>= 4.0.0)
    redis (5.3.0)
      redis-client (>= 0.22.0)
    redis-client (0.23.1)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    remotipart (1.4.4)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.1)
    rmagick (6.0.1)
      observer (~> 0.1)
      pkg-config (~> 1.4)
    rotp (6.3.0)
    rqrcode (2.2.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    rqrcode_png (0.1.5)
      chunky_png
      rqrcode
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-html-matchers (0.10.0)
      nokogiri (~> 1)
      rspec (>= 3.0.0.a)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.0)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.2)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-minitest (0.38.1)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rake (0.7.1)
      lint_roller (~> 1.1)
      rubocop (>= 1.72.1)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-ole (********)
    ruby-progressbar (1.13.0)
    ruby-rc4 (0.1.5)
    ruby-vips (2.2.4)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (2.4.1)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    savon (2.15.1)
      akami (~> 1.2)
      builder (>= 2.1.2)
      gyoku (~> 1.2)
      httpi (>= 4, < 5)
      mail (~> 2.5)
      nokogiri (>= 1.8.1)
      nori (~> 2.4)
      wasabi (>= 3.7, < 6)
    securerandom (0.4.1)
    selenium-webdriver (4.27.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    simple_form (5.3.1)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    sitemap_generator (6.3.0)
      builder (~> 3.0)
    slim (5.2.1)
      temple (~> 0.10.0)
      tilt (>= 2.1.0)
    spreadsheet (1.3.4)
      bigdecimal
      logger
      ruby-ole
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.23.2)
      base64
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    ssrf_filter (1.3.0)
    stringio (3.1.2)
    sucker_punch (3.2.0)
      concurrent-ruby (~> 1.0)
    summernote-rails (********)
      railties (>= 3.1)
    temple (0.10.3)
    tether-rails (1.4.0)
      rails (>= 3.1)
    thor (1.3.2)
    thread_safe (0.3.6)
    tilt (2.5.0)
    time_difference (0.5.0)
      activesupport
    timecop (0.9.10)
    timeout (0.4.3)
    trix-rails (2.4.0)
      rails (> 4.1)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    unicode_utils (1.4.0)
    unicorn (6.1.0)
      kgio (~> 2.6)
      raindrops (~> 0.7)
    uniform_notifier (1.16.0)
    unread (0.14.0)
      activerecord (>= 6.1)
    uri (1.0.3)
    useragent (0.16.11)
    vcr (6.3.1)
      base64
    view_component (3.23.2)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1)
      method_source (~> 1.0)
    virtus (2.0.0)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
    warden (1.2.9)
      rack (>= 2.0.9)
    wasabi (5.1.0)
      addressable
      faraday (>= 1.9, < 3)
      nokogiri (>= 1.13.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket (1.2.11)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yaml_db (0.7.0)
      rails (>= 3.0)
      rake (>= 0.8.7)
    zeitwerk (2.7.1)
    zip-zip (0.3)
      rubyzip (>= 1.0.0)

PLATFORMS
  ruby

DEPENDENCIES
  aasm
  active_admin_sidebar
  active_model_otp
  active_record_query_trace
  activeadmin
  acts_as_list
  acts_as_xlsx
  addressable
  avatarly
  awesome_print
  axlsx_styler
  bcrypt_pbkdf (>= 1.0, < 2.0)
  bootsnap
  bootstrap-sass
  brakeman
  browsernizer
  bullet
  bundler-audit
  cancancan
  capistrano-bundler
  capistrano-db-tasks!
  capistrano-rails
  capistrano-rvm (~> 0.1.0)
  capybara
  capybara-email
  capybara-screenshot
  capybara-select2
  carrierwave
  carrierwave-base64
  caxlsx_rails
  certified
  chronic (= 0.10.2)
  chunky_png
  cmxl
  cocoon
  coffee-rails
  combine_pdf
  d3-rails
  daemons
  data-confirm-modal
  database_cleaner
  decent_exposure
  descriptive-statistics
  devise
  devise-security
  dkim
  ed25519 (>= 1.2, < 2.0)
  email_spec
  email_validator
  enumerize
  exception_notification
  factory_bot_rails
  faker
  font-awesome-rails (~> 4.7)
  geocoder
  gon
  groupdate
  gruff (~> 0.6.0)
  high_voltage
  httparty
  icalendar
  jquery-rails
  jquery-timepicker-addon-rails
  jquery-ui-rails
  json
  just-datetime-picker
  kaminari
  launchy
  listen
  locales_export_import
  meta-tags
  mime-types
  mimemagic!
  minitest-focus
  minitest-stub-const
  mocha
  nokogiri
  oj
  paper_trail
  parallel_tests
  paranoia
  paranoia_uniqueness_validator
  pdf-inspector
  pdf-reader (~> 2.14.1)
  pg
  phantomjs
  pluck_all
  pluck_to_hash
  poltergeist
  prawn (~> 2.2)
  prawn-markup
  prawn-table
  prawn-templates (~> 0.1.1)
  puma
  pundit
  rack-mini-profiler
  rails (= 8.0.2)
  rails-assets-bootbox (~> 4.4.0)
  rails-assets-remarkable-bootstrap-notify!
  rails-controller-testing
  rails-patch-json-encode
  rails_performance
  ransack
  rdoc (= 6.14.0)
  redis-namespace
  remotipart (~> 1.2)
  rfc822!
  rmagick
  rqrcode_png
  rspec-html-matchers
  rspec-rails
  rubocop
  rubocop-minitest
  rubocop-performance
  rubocop-rails
  rubocop-rake
  rubocop-rspec
  rubyzip (>= 1.0.0)
  sass-rails
  savon
  select2-rails (= 3.4.1)!
  selenium-webdriver
  simple_form
  simplecov
  sitemap_generator
  slim
  spreadsheet
  sprockets-rails
  sucker_punch
  summernote-rails
  tether-rails
  time_difference
  timecop
  trix-rails
  unicode_utils
  unicorn (>= 4.3.1)
  unread
  vcr
  view_component
  virtus
  web-console
  webmock
  whenever
  yaml_db
  zip-zip

RUBY VERSION
   ruby 3.3.5p100

BUNDLED WITH
   2.6.2
