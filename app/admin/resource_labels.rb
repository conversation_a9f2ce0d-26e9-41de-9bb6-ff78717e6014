ActiveAdmin.register ResourceLabel do
  menu false

  belongs_to :label, optional: true, parent_class: Label

  form do |f|
    resource_type = params[:resource_type] || 'ClinicalCenter'

    f.semantic_errors
    f.inputs do
      f.input :label
      f.hidden_field :resource_type, value: resource_type
      f.input :resource_id, collection:  resource.label.resource.send(resource_type.snakecase.pluralize), as: :select
      f.input :valid_to, as: :datepicker
    end
    f.actions
  end
end
