ActiveAdmin.register_page 'Statystyki' do
  content do
    params[:use_html] = true
    page = ProjectsStatisticsPage.new(params: params)

    render 'show', page: page
  end

  page_action :generate_report, method: :get do
    page = ProjectsStatisticsPage.new(params: params)

    if params[:xls].present?
      render xlsx: "report", filename: "report.xlsx", disposition: "inline", locals: { page: page }
    else
      redirect_to admin_statystyki_path(params)
    end
  end
end