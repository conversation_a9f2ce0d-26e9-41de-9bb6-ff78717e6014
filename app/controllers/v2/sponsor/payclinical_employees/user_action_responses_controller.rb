class V2::Sponsor::PayclinicalEmployees::UserActionResponsesController < ApplicationController
  before_action :authenticate_researcher!

  def index
    authorize current_researcher, :payclinical_employee?

    @projects = Project.select(%i[id clinical_protocol_code]).order('clinical_protocol_code asc')
    @clinical_users = ClinicalUser.order('patient_code asc').pluck_array(:id, :patient_code)
    @researchers = Researcher.order('last_name asc, first_name asc')

    @user_action_responses = UserActionResponse
      .joins(:user_action_requirement)
      .merge(UserActionRequirement.not_resolved)
      .order('user_action_responses.created_at desc')
    @q = @user_action_responses
      .ransack(params[:q])
    @user_action_responses = @q
      .result
      .includes(:researcher, user_action_requirement: [clinical_user: [:clinical_center, project: [:contract_research_organization]]])
      .page(params[:page])
  end
end
