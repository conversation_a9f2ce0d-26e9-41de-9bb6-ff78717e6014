class V2::Sponsor::HelpdeskEmailsController < ApplicationController
  include HelpdeskEmails::Assignable

  before_action :authenticate_researcher!
  before_action :authorize_researcher, except: [:new, :create]
  before_action :get_email, only: %i[show toggle_priority reject]

  def index
    @senders = Researcher.joins(:helpdesk_emails).distinct.order('email asc')
    @cros = ContractResearchOrganization.order(name: :asc)
    @projects = Project.order(clinical_protocol_code: :asc)
    @operators = Researcher.operators.distinct.order('last_name asc')

    @scope = params[:scope] || 'new_or_pending_or_suspended'
    allowed_scopes = %w[processed removed new_or_pending]

    if allowed_scopes.include?(@scope)
      session[:he_index_scope] = @scope
    end

    @emails = if @scope != 'all' && allowed_scopes.include?(@scope)
                HelpdeskEmail.send(@scope).not_responses.order('message_date desc, id desc')
              else
                HelpdeskEmail.not_responses.order('message_date desc, id desc')
    end

    @emails_count = @emails.count
    if params[:my_emails] == 'true'
      @emails = @emails.where(operator_id: current_researcher.id)
    end
    @my_emails_count = @emails.where(operator_id: current_researcher.id).not_responses.size

    params[:q] ||= {}

    if params[:q][:created_at_gteq].nil? && params[:q][:created_at_lteq].nil?
      params[:q][:created_at_gteq] ||= '01-06-2025'
    end

    @q = @emails.ransack(params[:q])
    @emails = @q
              .result
              .includes(
                :researcher,
                :operator,
                project: [:fee_plan],
                contract_research_organization: [:secondary_operator, :primary_operator, :fee_plan],
              )
              .not_responses
              .page params[:page]
  end

  def show
    authorize @email, :show?

    @responses = @email.helpdesk_responses.order('id desc')
    @comments = @email
      .comments
      .includes(:added_by)
      .order_by_id
  end

  def edit
    @email = HelpdeskEmail::ForCheckIn.find(params[:id])
    authorize @email, policy_class: HelpdeskEmailPolicy
    set_data_for_he_assignment

    if @cro
      @email.primary_operator_id = @cro.primary_operator_id || current_researcher.id
      @email.secondary_operator_id = @cro.secondary_operator_id
    end
  end

  def update
    @email = HelpdeskEmail::ForCheckIn.find(params[:id])
    authorize @email, policy_class: HelpdeskEmailPolicy

    unless @email.checked_in_at
      @email.checked_in_at = Time.current
    end

    @email.set_tag

    if @email.update(update_params.merge(status: 'pending'))
      redirect_to v2_sponsor_helpdesk_emails_path(scope: session[:he_index_scope])
    else
      redirect_back_with_default alert: @email.errors.full_messages.to_sentence
    end
  end

  def new
    @email = HelpdeskEmail::ByResearcherFromSystem.new(project_id: params[:project_id])

    if @email.project
      @email.contract_research_organization_id = @email.project.contract_research_organization_id
    end
  end

  def create
    @email = HelpdeskEmail::ByResearcherFromSystem.new(create_params)
    @email.researcher = current_researcher
    @email.from = current_researcher.email
    @email.status = 'new'
    @email.to = '<EMAIL>'
    @email.message_date = Time.current
    @email.skip_fix_if_forward = true

    if @email.save
      redirect_to v2_sponsor_projects_path, notice: "Your support request# #{ @email.receipt_id } has been sent to HelpDesk."
    else
      render :new
    end

  end

  # Custom member actions

  def toggle_priority
    authorize @email, :toggle_priority?

    if @email.update(priority: !@email.priority)
    else
      flash[:alert] = 'Toggle priority error.'
    end

    redirect_to v2_sponsor_helpdesk_emails_path
  end

  def reject
    authorize @email, :reject?

    if @email.update(status: :removed)
      set_current_researcher_as_operator
      flash[:notice] = 'Email has been rejected.'
      redirect_to v2_sponsor_helpdesk_emails_path
    else
      flash[:alert] = 'Reject error.'
      render :show
    end
  end

  private

  def get_email
    @email = HelpdeskEmail.find(params[:id])
  end

  def set_current_researcher_as_operator
    @email.update(operator_id: current_researcher.id)
  end

  def authorize_researcher
    authorize current_researcher, :helpdesk_operator?
  end

  def create_params
    params[:helpdesk_email_by_researcher_from_system].permit(
      :project_id,
      :contract_research_organization_id,
      :subject,
      :body
    )
  end

  def update_params
    params[:helpdesk_email_for_check_in].permit(
      :from,
      :cc,
      :subject,
      :contract_research_organization_id,
      :primary_operator_id,
      :secondary_operator_id,
      :project_id,
      :operator_id,
      :request_type,
    )
  end
end
