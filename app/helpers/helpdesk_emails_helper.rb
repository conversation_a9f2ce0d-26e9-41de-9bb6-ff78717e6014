module HelpdeskEmail<PERSON><PERSON><PERSON><PERSON>
  def helpdesk_email_actions(email:, operator:, form:)
    return nil if email.operator != operator

    links = []

    links << form.submit('Update', class: 'btn btn-info m-0 mr-1')
    if HelpdeskEmailPolicy.new(current_researcher, email).create_ticket?
      links << link_to('Create a ticket', new_v2_sponsor_helpdesk_email_helpdesk_email_ticket_path(email), class: 'btn btn-info m-0 mr-1')
    end
    if HelpdeskEmailPolicy.new(current_researcher, email).escalate?
      links << link_to('Escalate', new_v2_sponsor_helpdesk_email_helpdesk_email_escalation_path(email), class: 'btn btn-info m-0 mr-1')
    end
    if HelpdeskEmailPolicy.new(current_researcher, email).reply?
      links << link_to('Respond', new_v2_sponsor_helpdesk_email_response_path(email), class: 'btn btn-info m-0 mr-1')
    end
    if HelpdeskEmailPolicy.new(current_researcher, email).create_shipment?
      links << link_to('Register forms', new_v2_sponsor_helpdesk_email_helpdesk_email_shipment_path(email), class: 'btn btn-info m-0 mr-1')
    end
    links << link_to('Back', v2_sponsor_helpdesk_emails_path, class: 'btn btn-warning m-0 mr-1')

    if HelpdeskEmailPolicy.new(current_researcher, email).reject?
      links << link_to('Trash', reject_v2_sponsor_helpdesk_email_path(email), method: :post, class: 'btn btn-danger float-lg-right m-0 mr-0')
    end

    links.join.html_safe
  end

  def helpdesk_email_high_priority_icon(email:, link: true)
    icon_class = email.priority ? 'fa-star' : 'fa-star-o'

    if link
      link_url = toggle_priority_v2_sponsor_helpdesk_email_path(email)
      method = :put
    else
      link_url = '#'
      method = :get
    end

    nr_of_stars = if email.vip?
                    3
                  elsif email.researcher&.cro_roles&.exists?
                    2
                  elsif email.researcher&.highest_role == 'Manager'
                    1
                  else
                    0
                  end
    link_to link_url,
            method: method, class: 'protip',
            data: { 'pt-title' => t('priority_flag'), 'pt-position' => 'top' } do
      nr_of_stars.times.map do
        high_priority_icon(klass: icon_class, color_class: email.priority ? 'red' : 'gray')
      end.join.html_safe
    end
  end

  def helpdesk_email_subheader(email:)
    link = link_to "##{email.receipt_id}", v2_sponsor_helpdesk_email_path(@email), class: 'red'
    "Email request #{link}"
  end

  def high_priority_icon(klass: 'fa-star', color_class: 'red', tooltip: false, tooltip_title: '')
    data = if tooltip
             { pt_position: 'bottom', pt_title: tooltip_title }
           else
             {}
    end
    klass << ' protip' if tooltip
    content_tag :i, nil, class: "#{color_class} fa #{klass}", data: data
  end

  def formatted_helpdesk_email_body(body)
    body.gsub(/(\r\n){2,}/, '<br/><br/>').gsub(/\n/, '<br/>').html_safe
  end

  def new_helpdesk_email_link(subject: nil, klass: 'default_sm_btn pdf_btn', helpdesk_project: nil)
    link_to 'Helpdesk', new_v2_sponsor_helpdesk_email_path(project_id: helpdesk_project&.id), class: klass, id: 'new_hd_link'
  end

  def with_he_colors(he, body)
    return tag.span body, class: 'red' if !he.new? && he.response_time_limit_not_met?

    body
  end

  def helpdesk_summary
    "We have received #{ HelpdeskEmail.created_today.count } requests today. Requests resolved today: #{ HelpdeskEmail.where(resolved_at: Time.current.all_day).count }."
  end
end
