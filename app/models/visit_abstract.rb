class VisitAbstract < ApplicationRecord
  self.table_name = 'visits'

  ### CONSTANTS
  STATES = [
    UNPLANNED = 'u'.freeze, # niezaplanowana
    PLANNED = 'p'.freeze, # zaplanowana w przyszlosci
    WAITING = 'w'.freeze, # odbyla sie i oczekuje na płatność
    SUCCESS = 's'.freeze, # odbyla sie pieniadze zaplacone
    VERIFIED = 'v'.freeze,
    NOT_APPLICABLE = 'n'.freeze
  ].freeze

  STATES_DICT_PL = {
    I18n.t('activerecord.attributes.visit.states.u') => 'u', # Niezaplanowana,nieodbyta, nie ma daty
    I18n.t('activerecord.attributes.visit.states.p') => 'p', # Zaplanowana, nieodbyta, jest data
    I18n.t('activerecord.attributes.visit.states.w') => 'w', # Oczekuj<PERSON><PERSON> na płat<PERSON>', odbyta
    I18n.t('activerecord.attributes.visit.states.s') => 's', # Opłacona
    I18n.t('activerecord.attributes.visit.states.v') => 'v', # Zweryfikowana
    I18n.t('activerecord.attributes.visit.states.v') => 'n'
  }.freeze

  STATES_DICT_PL_INV = {
    'u' => I18n.t('activerecord.attributes.visit.states.u'), # Niezaplanowana,nieodbyta, nie ma daty
    'p' => I18n.t('activerecord.attributes.visit.states.p'), # Zaplanowana, nieodbyta, jest data
    'w' => I18n.t('activerecord.attributes.visit.states.w'), # Oczekująca na płatność', odbyta
    's' => I18n.t('activerecord.attributes.visit.states.s'), # Opłacona
    'v' => I18n.t('activerecord.attributes.visit.states.v'), # Zweryfikowana
    'v' => I18n.t('activerecord.attributes.visit.states.n')
  }.freeze

  scope :unplanned, -> { where state: 'u' }
  scope :planned, -> { where state: 'p' }
  scope :not_reimbursed, -> { where(state: [PLANNED, UNPLANNED]) }
  scope :waiting, -> { where state: 'w' }
  scope :success, -> { where state: 's' }
  scope :verified, -> { where state: 'v' }
  scope :happened, -> { where state: %w[v s w] }
  scope :not_happened, -> { where state: %w[p u] }
  scope :sorted_by_position, -> { includes(:visit_type).order('visit_types.position ASC') }
  scope :unpaid, -> { where('visits.state in (?)', [WAITING, PLANNED]) }
  scope :unpaid_or_unplanned, -> { where('state in (?)', [WAITING, PLANNED, UNPLANNED]) }
  scope :paid, -> { where('visits.state in (?)', [SUCCESS, VERIFIED]) }
  scope :state_is, ->(state) { where('state in (?)', state) }
  scope :scheduled, -> { left_joins(:visit_type).where('visit_types.exceptional = FALSE') }
  scope :self_added, -> { left_joins(:visit_type).where('visit_types.exceptional = TRUE') }
  scope :with_set_date, -> { where('visit_date is not null') }
  scope :unpaid_with_set_date, -> { with_set_date.unpaid }
  scope :waiting_with_set_date, -> { with_set_date.waiting }
  scope :transfer_waiting, -> { joins(:clinical_transfers).where(clinical_transfers: { state: ClinicalTransfer::WAITING }).distinct }
  scope :transfer_in_progress, -> { joins(:clinical_transfers).where(clinical_transfers: { state: [ClinicalTransfer::PROCESSING, ClinicalTransfer::DEBIT_WAITING] }).distinct }
  scope :transfer_paid, -> { success.joins(:clinical_transfers).merge(ClinicalTransfer.sent) }
  scope :transfer_paid_or_in_progress, -> { joins(:clinical_transfers).where(clinical_transfers: { state: [ClinicalTransfer::PROCESSING, ClinicalTransfer::DEBIT_WAITING, ClinicalTransfer::SENT_TO_BANK, ClinicalTransfer::BOOKED, ClinicalTransfer::GROUPED] }).distinct }
  scope :transfer_paid_or_grouped, -> { success.joins(:clinical_transfers).merge(ClinicalTransfer.sent_or_grouped) }
  scope :ordered_by_visit_date_and_position, -> { left_joins(:visit_type).order('visit_date asc, visit_types.position asc') }
  scope :not_applicable, -> { where(state: NOT_APPLICABLE) }
  scope :applicable, -> { where('visits.state != ?', NOT_APPLICABLE) }
  scope :high_priority, -> { where(high_priority: true) }
  scope :without_transfer, -> { includes(:clinical_transfers).where(clinical_transfers: { id: nil }) }
  scope :without_transfer_or_with_rejected_transfers, -> { left_joins(:clinical_transfers).where('clinical_transfers.id IS NULL OR clinical_transfers.state = ?', ClinicalTransfer::CANCELED) }
  scope :to_be_reimbursed, -> { with_set_date.applicable.unpaid }
  scope :not_test_project, -> { joins(clinical_user: :project).where(projects: { test_project: false }) }
  scope :transfer_not_waiting,  -> { joins(:clinical_transfers).where('clinical_transfers.state != ?', ClinicalTransfer::WAITING).distinct }
  scope :from_template, -> { where('project_visit_template_id is not null') }
  scope :paid_to_clinical_center, -> { where(transfer_to: 'clinical_center') }
  scope :at_clinical_centers, ->(clinical_centers) { joins(:clinical_user).where(clinical_users: { clinical_center_id: clinical_centers }) }
  scope :of_clinical_users, ->(clinical_users) { joins(:clinical_user).where(clinical_users: { id: clinical_users }) }
  scope :clean, -> { where(visit_date: nil).includes(:visit_payment_categorizations).where(visit_payment_categorizations: { visit_id: nil }) }

  attr_reader :state_str

  def self_added?
    visit_type.try(:exceptional)
  end

  def template_visit?
    !visit_type.try(:exceptional)
  end

  def not_applicable?
    state == NOT_APPLICABLE
  end

  def not_happened?
    [PLANNED, UNPLANNED].include? state
  end

  def unpaid?
    [WAITING, PLANNED].include? state
  end

  def paid?
    if defined?(@paid)
      @paid
    else
      @paid = [SUCCESS, VERIFIED].include?(state) && !clinical_transfers.waiting.exists?
    end
  end

  def success?
    state == SUCCESS
  end

  def waiting?
    state == WAITING
  end

  def waiting_for_transfer_confirmation?
    if defined?(@waiting_for_transfer_confirmation)
      @waiting_for_transfer_confirmation
    else
      @waiting_for_transfer_confirmation = clinical_transfers.waiting.exists?
    end
  end

  def transfer_sent_to_bank?
    clinical_transfer&.sent?
  end

  def state_str
    actual_state
      # I18n.t("activerecord.attributes.visit.states.#{self.state}")
    end

  def self.happened_unpaid
    Visit.where(state: 's').select { |v| %w[d p].include? v.clinical_transfer.try(:state) }
  end

  ### VALIDATIONS

  validates :state, inclusion: { in: VisitAbstract::STATES }
  validates :prescription, length: { maximum: 1024 }, allow_blank: true
  validates :description, length: { maximum: 512 }, allow_blank: true
  validates :name, presence: true

  validate :visit_date_required
  validate :visit_date_in_future

  before_save :remove_high_priority_flag

  def actual_state
    ct_state = transfer_state

    if ct_state == ClinicalTransfer::CANCELED
      return I18n.t('activerecord.attributes.visit.actual_states.rejected')
    end

    return state_human_name unless state == SUCCESS
    return state_human_name unless ct_state

    if ct_state == ClinicalTransfer::WAITING
      I18n.t('activerecord.attributes.visit.actual_states.waiting')
    elsif ct_state == ClinicalTransfer::DEBIT_WAITING
      I18n.t('activerecord.attributes.visit.actual_states.debit_waiting')
    elsif ct_state == ClinicalTransfer::PROCESSING
      I18n.t('activerecord.attributes.visit.actual_states.processing')
    elsif ct_state == ClinicalTransfer::SENT_TO_BANK
      I18n.t('activerecord.attributes.visit.actual_states.sent_to_bank')
    elsif ct_state == ClinicalTransfer::CANCELED
      I18n.t('activerecord.attributes.visit.actual_states.canceled')
    else
      '-'
    end
  end

  def find_visit_action(actions:)
    return_va = nil
    actions.reverse.each do |action|
      next if return_va

      va = visit_actions.with_action_name(action).try(:last)
      return_va = va if va
    end

    return_va
  end

  def allowed_stepper_actions
    actions = VisitAction::ACTION_NAMES

    # optional states
    if visit_actions.with_action_name('awaiting_approval').blank?
      actions -= [:awaiting_approval]
    end

    actions -= if project.debit_allowed == false
                 %i[
                   batch_not_signed
                   waiting_for_debit_note
                   unpaid_debit_note
                   paid_note_cu_confirmed_paid_to_bank
                   paid_note_cu_confirmed_paid_to_post
                   paid_note_cu_unconfirmed_paid_to_lmp
                   paid_note_cu_confirmed_paid_to_site
                 ]
               else
                 []
               end

    hash = {}

    actions.each do |action|
      action_human_name = VisitAction.human_action_name(action)
      if hash[action_human_name]
        hash[action_human_name] << action
      else
        hash[action_human_name] = [action]
      end
    end

    hash
  end

  def visit_actions_hash(with_deleted: false)
    hash = {}
    actions = visit_actions
    actions = actions.with_deleted if with_deleted

    actions.each do |va|
      action_human_name = va.human_action_name

      if hash[action_human_name]
        hash[action_human_name] << va
      else
        hash[action_human_name] = [va]
      end
    end

    hash
  end

  def transfer_in_progress?
    clinical_transfer && [ClinicalTransfer::PROCESSING, ClinicalTransfer::DEBIT_WAITING].include?(clinical_transfer.state)
  end

  def transfer_state
    return unless clinical_transfer

    ct = if clinical_transfer.master_transfer_id.present?
           clinical_transfer.master_transfer
         else
           clinical_transfer
         end

    ct.try(:state)
  end

  def transfer_waiting?
    transfer_state == ClinicalTransfer::WAITING
  end

  def transfer_debit_waiting?
    transfer_state == ClinicalTransfer::DEBIT_WAITING
  end

  def transfer_accepted?
    clinical_transfer.try(:transfer_accepted_by_researcher_id)
  end

  def transfer_sent?
    clinical_transfer.try(:sent?) || clinical_transfer&.grouped?
  end

  def state_human_name
    I18n.t("activerecord.attributes.visit.states.#{state}")
  end

  def visit_date_required
    if visit_date.blank? && %w[v s w].include?(state)
      errors.add(:visit_date, :blank)
    end
  end

  def may_toggle_high_priority?
    ct_state = transfer_state
    [PLANNED, WAITING].include?(state) || (ct_state == ClinicalTransfer::WAITING)
  end

  def may_send_reminder?
    [UNPLANNED, PLANNED, WAITING].include?(state)
  end

  def remove_high_priority_flag
    self.high_priority = false if high_priority && %w[s u].include?(state)
  end

  def visit_stepper_available?
    return false unless latest_visit_action

    true
  end

  def visit_date_in_future
    return if respond_to?(:project) && project&.future_visits_allowed

    if visit_date && (visit_date > Time.current) && %w[v s w].include?(state)
      errors.add(:visit_date, I18n.t('exceptions.cannot_accept_visit_with_visit_date_in_future'))
    end
  end
end
