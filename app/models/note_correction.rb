class NoteCorrection < ApplicationRecord
  include NoteNumerable
  include HasCorrectiveNote

  extend Enumerize

  serialize :clinical_transfer_ids

  validates :clinical_transfer_ids, :project_debit_summary_id, presence: true

  belongs_to :project_debit_summary

  after_create :process

  delegate_missing_to :project_debit_summary

  enumerize :state, in: [:created, :sent], predicates: true

  def amount_for_fakir
    0 - amount
  end

  def process
    NoteCorrection.transaction do
      cancel_transfers
      generate_pdf
    end
  end

  def clinical_transfers
    ClinicalTransfer.where(id: clinical_transfer_ids)
  end

  def amount
    clinical_transfers.sum(:amount)
  end

  def summary_date
    project_debit_summary.created_at
  end

  def cancel_transfers
    clinical_transfers.each do |ct|
      form = CancelVisitTransferForm.new(visit_id: ct.visit_id, skip_researcher_validation: true, skip_visit_revertibleness_validation: true, visit_transfer: ct)
      form.save
    end
  end

  def generate_pdf
    file_name = "public/system/note_corrections/#{self.note_number.gsub(/ /,'_')}.pdf"
    NoteCorrectionPdf.new(note_correction: self).render_file file_name
    self.update!(pl_file_path: file_name.gsub!(/public\//,''))
  end
end
