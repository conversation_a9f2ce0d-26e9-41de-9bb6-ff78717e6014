class ZadarmaEvent < ApplicationRecord
  extend Enumerize

  enumerize :status, in: %i[new pending helpdesk closed], default: :new, predicates: true, scope: true

  attribute :without_project, :boolean, default: false

  belongs_to :project
  belongs_to :contract_research_organization
  belongs_to :operator, class_name: 'Researcher'
  belongs_to :researcher # badacz k<PERSON> dzwonil
  belongs_to :called_back_by, class_name: 'Researcher'

  before_create :set_researcher
  before_save :add_plus_to_called_number

  delegate :clinical_protocol_code, to: :project, allow_nil: true

  [:fee_plan].each do |name|
    define_method(name) do
      project ? project.send(name) : contract_research_organization&.send(name)
    end
  end

  scope :new_or_pending, -> { where(status: [:new, :pending]) }
  scope :resolved, -> { where(status: [:helpdesk, :closed]) }
  scope :not_internal, -> { where("LENGTH(caller_number) != 3") }
  scope :call_starts, -> { where(event: 'NOTIFY_START') }

  def response_time_left
    return @response_time_left if defined?(@response_time_left)

    time_limit = (fee_plan&.response_time || contract_research_organization&.fee_plan&.response_time || 48) * 60

    @response_time_left = time_limit - ((Time.zone.now - (checked_in_at || created_at)) / 1.minute).to_i
  end

  def response_time_limit_not_met?
    response_time_left < 0
  end

  private

  def set_researcher
    self.researcher_id ||= Researcher.find_by(phone_number: caller_number)&.id
  end

  def add_plus_to_called_number
    return if called_number.blank?

    self.called_number = "+#{called_number}" unless called_number&.start_with?('+')
  end
end
