class ProjectReturnFundsNote < ApplicationRecord
  include NoteNumerable
  include HasCorrectiveNote

  belongs_to :project

  validates :project, :amount, :account_number, presence: true

  has_one_attached :pdf

  after_create :create_pdf

  delegate_missing_to :project

  def create_pdf
    content = ProjectReturnFundsNotes::Pdf.new(
      project_return_funds_note: self
    ).render

    pdf.attach(io: StringIO.new(content), filename: "#{ file_name }.pdf")
  end

  def file_name
    "Nota uznaniowa #{number}"
  end

  def title
    'Zwrot depozytu z przedpłaconego konta badania klinicznego'
  end

  def set_number
    self.number = "#{sprintf("%.4i", calculate_note_count_in_current_month)}-#{created_at.strftime("%m%y")}-#{sprintf("%.4i", project.contract_research_organization_id)}-#{sprintf("%.4i", project_id)}"
    self.save! validate: false
  end

  def total
    amount
  end

  def end_date
    created_at
  end

  def paid_at
    created_at
  end

  def note_number
    number
  end

  def paid?
    true
  end

  def original_amount
    amount
  end
end
