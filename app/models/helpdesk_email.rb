class HelpdeskEmail < ApplicationRecord
  include Resolvable

  PROCESSED = [:escalated, :ticketed, :resolved_by_phone, :resolved, :cancelled, :rejected]
  RECEIPT_ID_PREFIX = 'HD/'

  has_paper_trail

  extend Enumerize
  self.inheritance_column = nil

  attr_accessor :skip_autorespond,
                :skip_fix_if_forward,
                :skip_task_category_notifications,
                :skip_task_category_sms_notifications,
                :task_category_cc_emails
  attribute :contains_personal_data_form, :boolean, default: false

  enumerize :status, in: %i[new pending ticketed replied removed resolved_by_phone resolved escalated cancelled closed suspended], default: :new, predicates: true, scope: true
  enumerize :type, in: [:normal, :complaint, :incident, :response], default: :normal, predicates: true, scope: true
  enumerize :request_type, in: [
    'Form', # formularz wgrany poprzez upload
    'Email', # stworzone przez przychodzacy email
    'Event', # wydarzenie systemowe
    'Internal', # wydarzenie stworzone przez pracownikow Payclinical
    'Phone',
    'Subject'
  ]
  enumerize :tag, in: [:vip, :safe, :risk, :plan]

  has_many :helpdesk_email_attachments, dependent: :destroy
  has_many :attachments, class_name: 'HelpdeskEmailAttachment'
  accepts_nested_attributes_for :attachments
  belongs_to :operator, class_name: 'Researcher'
  belongs_to :ticket, class_name: 'Message'
  belongs_to :resolved_by, polymorphic: true
  belongs_to :shipment, class_name: 'Shipment'
  belongs_to :project
  belongs_to :contract_research_organization
  has_many :helpdesk_responses
  belongs_to :researcher #badacz ktory wyslal
  has_many :helpdesk_email_calls
  has_many :helpdesk_workloads
  belongs_to :clinical_user_form
  has_many :comments, as: :resource
  belongs_to :task_category, class_name: 'HelpdeskEmails::TaskCategory', foreign_key: :task_category_id
  belongs_to :zadarma_event

  before_create :set_original_body
  before_create :fix_if_forward, unless: :skip_fix_if_forward
  before_create :set_researcher
  before_create :act_as_postal_transfer
  before_create :set_as_priority_if_from_manager
  before_create :set_tag
  before_save :remove_helpdesk_email_from_cc
  before_save :remove_newlines
  after_create :set_cro_based_on_researcher, unless: :contract_research_organization_id
  before_save :enable_call_required, if: -> { resolved? && status_changed? && !shipment_id }
  after_save :update_clinical_user_form, if: proc { clinical_user_form && shipment && saved_change_to_shipment_id? }
  after_create :set_receipt_id
  after_create :create_response
  after_create :autorespond, unless: -> { type == 'response' }
  before_save { |he| he.request_type = 'Form' if contains_personal_data_form }
  after_save :send_task_category_notifications
  before_create :set_project_and_cro_from_zadarma_event, if: :zadarma_event
  before_create { |he|he.skip_autorespond = true if he.zadarma_event }
  after_create :update_zadarma_event, if: :zadarma_event
  before_save { |he|he.vip = true if he.researcher_id_changed? && he.researcher&.vip?  }

  before_validation :set_subject
  validates :from, :to, :subject, presence: true
  validate :blacklist_sender

  scope :new_or_pending, -> { where(status: [:new, :pending, :replied]) }
  scope :new_or_pending_or_suspended, -> { where(status: [:new, :pending, :replied, :suspended]) }
  scope :removed, -> { where(status: :removed) }
  scope :replied, -> { where(status: [:replied]) }
  scope :processed, -> { where(status: PROCESSED) }
  scope :open_complaints, -> { with_type(:complaint).new_or_pending }
  scope :resolved_complaints, -> { with_type(:complaint).where(status: [:resolved_by_phone, :resolved]) }
  scope :call_required, -> { where(call_required: true) }
  scope :not_responses, -> { where.not(type: 'response') }
  scope :status_new, -> { where(status: 'new') }
  scope :processing, -> { where(status: ['pending', 'replied']) }
  scope :new_or_processing, -> { where(status: ['pending', 'replied', 'new']) }
  scope :from_premium_projects, -> { left_joins(:project, :contract_research_organization).where('projects.fee_plan_cost > 0 OR contract_research_organizations.fee_plan_cost > 0') }
  scope :new_processing_from_premium_projects, -> { new_or_processing.from_premium_projects }
  scope :new_processing_from_premium_projects_older_than_2_days, -> { new_processing_from_premium_projects.where('helpdesk_emails.created_at <= ?', 2.days.ago) }
  scope :new_processing_from_vip_older_than_2_days, -> { new_or_processing.where('helpdesk_emails.created_at <= ?', 2.days.ago).where(tag: 'vip') }

  delegate :importance, to: :contract_research_organization, prefix: :cro, allow_nil: true
  [:fee_plan_name_only,
    :fee_plan,
    :free_plan?,
    :premium_plan?].each do |name|
      define_method(name) do
        project ? project.send(name) : cro&.send(name)
      end
    end

  def processed?
    PROCESSED.include?(status&.to_sym)
  end

  def status_change_date(target)
    if target == 'resolved'
      resolved_at
    else
      versions.where('object_changes like ?', "%status:\n- %\n- #{ target }%").order(id: :desc).last&.created_at
    end
  end

  def cro
    contract_research_organization
  end

  def set_subject
    unless self.subject.present?
      self.subject = 'Not provided'
    end
  end

  def remove_helpdesk_email_from_cc
    if self.cc.present?
      self.cc = self.cc.gsub('<EMAIL>', '')
    end
  end

  def remove_newlines
    self.body = self.body.try(:strip)
  end

  # Treat messeges from domains below as being forwarded, thus requiring further parsing
  def fix_if_forward
    forward = false
    ['@letmepay.com', '@noblewell.com', '@payclinical.com'].each do |domain|
      forward = true if from.include?(domain)
    end

    if forward
      result = {}
      result[:body] = ''
      line_index = 0
      to_index = 999_999

      if body
        body.split("\n").each do |line|
          line = line.gsub('> ', '')
          line = line.gsub('=0D', '')

          if line.downcase.starts_with?('to: ') && result[:to].nil?
            line.slice! 'To: '
            result[:to] = line
            to_index = line_index
          end

          if line.downcase.starts_with?('from: ') && result[:from].nil?
            line.slice! 'From: '
            result[:from] = line
          end

          if line.downcase.starts_with?('cc: ') && result[:cc].nil?
            line.slice! 'CC: '
            line.slice! 'Cc: '
            line.slice! 'cc: '
            result[:cc] = line.split(' ').join(', ')
          end

          if line.downcase.starts_with?('date: ') && result[:date].nil?
            line.slice! 'Date: '
            result[:date] = line.to_datetime
          end

          if line.downcase.starts_with?('subject: ') && result[:subject].nil?
            line.slice! 'Subject: '
            result[:subject] = line
          end

          result[:body] += line + "\n" if line_index > (to_index + 1)

          line_index += 1
        end

        result[:from] = result[:from].scan(/\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b/i)[0] if result[:from]
      end

      self.cc = "#{result[:to]},#{result[:cc]}"
      self.from = result[:from] if result[:from].present?
      fix_cc
      self.subject = result[:subject] if result[:subject].present?
      self.body = result[:body] if result[:body].present?
      self.message_date = result[:date] if result[:date].present?
      self.autorespond_body = "We have received your message and will assist you within the contract response time. Please contact <a href='mailto:<EMAIL>'><EMAIL></a> if you have further requests."
    end
  end

  def blacklist_sender
    blacklist_sendenders = ['<EMAIL>']

    if blacklist_sendenders.include?(from.try(:strip))
      errors.add(:from, 'Forbidden. Sender blacklisted.')
    end
  end

  def act_as_postal_transfer
    if from == '<EMAIL>'
      str1_markerstring = "'"
      str2_markerstring = "'"

      self.skip_autorespond = true

      post_transfer_number = subject[/#{str1_markerstring}(.*?)#{str2_markerstring}/m, 1]
      post_transfer = PostTransfer.find_by_transfer_number(post_transfer_number)

      return unless post_transfer
      self.status = 'resolved'

      str1_markerstring_date = 'W dniu '
      str2_markerstring_date = ' została'

      date_from_email = body[/#{str1_markerstring_date}(.*?)#{str2_markerstring_date}/m, 1]
      date_from_email = date_from_email.to_datetime

      post_transfer.update(received_by_patient_on: date_from_email)
      true
    end
  end

  def autorespond
    return false if autorespond_sent_at.present?
    return true if skip_autorespond

    sms_sent = false

    if researcher && sms_autoresponse_body
      researcher.send_sms(sms_autoresponse_body, priority: false)
      sms_sent = true
    end

    HelpdeskMailer.with(
      helpdesk_email: self,
      researcher: researcher,
      subject: email_autoresponse_subject,
    ).autorespond.deliver if get_autorespond_body.present? && email_autoresponse_subject

    self.autorespond_sent_at = DateTime.now
    self.sms_sent = sms_sent
    self.save
  end

  def self.find_researcher(email)
    email = email&.downcase
    exceptions = {
      '@iqvia.com' => '@quintiles.com'
    }

    researcher = nil

    exceptions.keys.each do |key|
      return if researcher
      return if email.nil?

      if email.include?(key)
        researcher = Researcher.find_by_email(email) || Researcher.find_by_email(email.gsub(key, exceptions[key]))
      else
        researcher = Researcher.find_by_email(email)
      end
    end

    researcher
  end

  def to_dj?
    to == '<EMAIL>'
  end

  def fix_cc
    if cc.present?
      cc_header = cc.scan(/\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b/i)
      self.cc = cc_header.join(', ')
    end

    if to.present?
      to_header = to.scan(/\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b/i)
      self.to = to_header.join(', ')
    end
  end

  def get_autorespond_body
    if autorespond_body.present?
      autorespond_body
    else
      email_autoresponse_body
    end
  end

  def set_cro_based_on_researcher
    if researcher && researcher.contract_research_organizations.uniq.size == 1
      self.contract_research_organization_id = researcher.contract_research_organizations.first.id
      self.save! validate: false
    end
  end

  def set_researcher
    return if researcher_id

    self.researcher_id = HelpdeskEmail.find_researcher(from).try(:id)
  end

  def enable_call_required
    self.call_required = true
  end

  def set_original_body
    self.original_body = self.body
  end

  def update_clinical_user_form
    clinical_user_form.update! state: ClinicalUserForm.state.processed
  end

  def cc_email_addresses
    [self.cc&.split(', '), SiteSetting.helpdesk_email_response_cc&.split(',')].flatten.compact.uniq
  end

  def set_as_priority_if_from_manager
    if researcher && researcher.highest_role == 'Manager'
      self.priority = true
    end
  end

  def create_response
    HelpdeskEmails::HandleResponse.call(helpdesk_email: self)
  end

  def set_receipt_id
    return unless message_date.present?

    update_columns receipt_id: HelpdeskEmails::GenerateReceiptId.call(helpdesk_email: self)
  end

  def set_tag
    self.tag = HelpdeskEmails::GetTag.call(helpdesk_email: self)
  end

  def response_template_name
    return :not_registered unless researcher

    roles = researcher.project_roles.pluck(:project_role).uniq

    if roles.include?('Manager')
      :manager
    elsif roles.include?('CRA') || roles.include?('CRA+')
      :cra
    elsif roles.include?('Investigator') || roles.include?('Investigator+')
      :investigator
    end
  end

  def unregistered_email?
    return false if researcher

    Researcher.where(email: from&.downcase).none? && ClinicalUser.where(email: from&.downcase).none?
  end

  def response_time_left
    return @response_time_left if defined?(@response_time_left)

    time_limit = (fee_plan&.response_time || contract_research_organization&.fee_plan&.response_time || 48) * 60

    @response_time_left = time_limit - ((Time.zone.now - (checked_in_at || created_at)) / 1.minute).to_i
  end

  def response_time_limit_not_met?
    response_time_left < 0
  end

  private

  def sms_autoresponse_body
    return unless response_template_name

    SiteSetting.helpdesk_email_autorespond_sms_body(self)
  end

  def email_autoresponse_body
    return unless response_template_name

    SiteSetting.helpdesk_email_autorespond_email_body(self)
  end

  def email_autoresponse_subject
    return unless response_template_name

    SiteSetting.helpdesk_email_autorespond_email_subject(self)
  end

  def send_task_category_notifications
    return unless task_category && researcher
    return if skip_task_category_notifications

    status_change_name = if status_previously_changed?(from: 'new', to: 'pending')
                           'pending'
                         elsif status_previously_changed?(to: 'suspended')
                           'suspended'
                         elsif status_previously_changed?(from: 'suspended', to: 'pending')
                           'reactivated'
                         elsif status_previously_changed?(to: 'resolved')
                           'resolved'
                         elsif status_previously_changed?(to: 'closed')
                           'closed'
                         end
    return if status_change_name.blank?

    email_subject = task_category.email_subject_for(self, status_change_name)
    email_body = task_category.email_body_for(self, status_change_name)
    sms_body = task_category.sms_body_for(self, status_change_name)

    HelpdeskMailer.with(
      subject: email_subject,
      body: email_body,
      email_to: researcher.email,
      cc_emails: task_category_cc_emails
      ).task_category_notification.deliver if email_body.present? && email_subject.present?

    researcher.send_sms(sms_body) if sms_body.present? && !skip_task_category_sms_notifications
  end

  def set_project_and_cro_from_zadarma_event
    self.project ||= zadarma_event.project
    self.contract_research_organization ||= zadarma_event.contract_research_organization
  end

  def update_zadarma_event
    zadarma_event.update!(status: 'helpdesk')
  end
end
