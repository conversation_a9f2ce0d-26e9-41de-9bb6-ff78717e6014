class ProjectDebitSummary < ApplicationRecord
  include Note::Currencies

  has_paper_trail
  extend Enumerize

  belongs_to :contract_research_organization
  has_many :project_debits
  has_many :project_debit_signatures, through: :project_debits
  has_many :clinical_transfers, through: :project_debits
  has_many :visits, through: :clinical_transfers
  has_many :visit_payment_categorizations, through: :visits
  has_many :clinical_users, through: :clinical_transfers
  has_many :clinical_centers, through: :clinical_transfers
  has_many :direct_clinical_transfers, class_name: 'ClinicalTransfer'
  belongs_to :project, touch: true
  # has_one :contract_research_organization, through: :project
  has_many :pds_transfer_topups
  has_many :topup_transfers, through: :pds_transfer_topups, source: :clinical_transfer
  belongs_to :credited_by_researcher, foreign_key: :credited_by_researcher_id, class_name: 'Researcher'
  has_many :note_corrections
  has_many :currency_accounts, as: :resource
  has_one :currency_account, as: :resource
  has_many :currency_exchanges, as: :record
  belongs_to :manual_note_currency_account, class_name: 'CurrencyAccount', foreign_key: :manual_note_currency_account_id

  STATES = [
    CREATED = 'c'.freeze, # Wygenerowana nota obciążeniowa
    PAID = 'p'.freeze, # Opłacona nota obciążeniowa
    CANCELLED = 'z'.freeze # anulowane, np. po anulowaniu przelewu w projekcie z juz wygenerowana nota
  ].freeze

  NOTE_TYPES = [
    NORMAL = 0,
    CREDIT = 1
  ].freeze

  enumerize :priority, in: %i[normal high immediate], default: :normal, predicates: true
  enumerize :credit_source, in: %i[main_acc credit_capital], predicates: { prefix: true }
  enumerize :credit_payment_type, in: %i[main_acc credit_capital], predicates: { prefix: true }

  # validates :project_id, presence: true
  validates :state, inclusion: { in: STATES }
  validate :uniq_note_number_chunk, if: proc { |pds| pds.note_number.present? }

  after_create :set_debit_note_number
  after_create :create_currency_account
  after_save :create_visit_actions, if: proc { |pds| pds.paid? && pds.saved_change_to_state? }
  after_save :set_paid_at, if: proc { |pds| pds.paid? && pds.saldo == 0 && pds.paid_at.nil? }

  delegate :pds_pdf_template,
           :researcher_for_pds_email,
           :pds_email_cc,
           :locale,
           :bulk_notes,
           :fakir_kod_kontrahenta,
           :fakir_opis_dokumentu,
           :fakir_nota_CT,
           :fakir_nota_DT,
           :fakir_nota_ct_2,
           :fakir_nota_dt_2,
           :exchange_diff_ct_3,
           :po_required_for_post_paid_note,
           to: :contract_research_organization

  delegate :country_code,
           :name,
           :company_nip,
           :company_zip_code,
           :company_city,
           :company_street,
           :company_name,
           to: :contract_research_organization, allow_nil: true, prefix: :cro

  delegate :clinical_protocol_code, :pdf_researcher_id_name, to: :project, allow_nil: true

  scope :pds_available_for_top_up, -> { where(state: [ProjectDebitSummary::CREATED]).where('saldo < ? and project_id is not null', 0) }
  scope :with_note_numbers, -> { where('note_number is not null') }
  scope :not_paid, -> { not_cancelled.where('(project_debit_summaries.note_type = 0 AND project_debit_summaries.state != ?) OR (project_debit_summaries.note_type = 1 AND project_debit_summaries.credit_paid_at IS NULL)', PAID) }
  scope :normal_unpaid, -> { not_cancelled.normal.where.not(state: PAID) }
  scope :debit, -> { where(manual_amount: nil) }
  scope :credit, -> { where('manual_amount is not null') }
  scope :normal, -> { where(note_type: NORMAL) }
  scope :credit_note, -> { where(note_type: CREDIT) }
  scope :credit_note_paid, -> { where('credit_paid_at IS NOT NULL') }
  scope :credit_note_unpaid, -> { credit_note.where(credit_paid_at: nil) }
  scope :never_credited, -> { normal.where(credit_paid_at: nil, credited_at: nil) }
  scope :credited, -> { where('credited_at IS NOT NULL OR credit_paid_at IS NOT NULL') }
  scope :any_note_type, -> { where(note_type: NOTE_TYPES) }
  scope :is_valid, -> { where(is_valid: true) }
  scope :paid, -> { where(state: PAID) }
  scope :created, -> { where(state: CREATED) }
  scope :paid_but_transfers_not_sent, -> { paid.joins(:clinical_transfers).where('clinical_transfers.state NOT IN (?)', [ClinicalTransfer::SENT_TO_BANK, ClinicalTransfer::BOOKED, ClinicalTransfer::CANCELED]).distinct }
  scope :not_cancelled, -> { where('project_debit_summaries.state != ?', CANCELLED) }
  scope :negative_balance, -> { where('project_debit_summaries.saldo < 0') }
  scope :waiting, -> { where(state: [CREATED]) }
  scope :sent, -> { where.not(sent_at: nil) }

  def credit_unpaid?
    credit? && !credit_paid_at
  end

  def amount_left_to_pay
    credit? ? (currency_exchange&.amount || original_amount) : saldo.abs
  end

  def to_s
    note_number
  end

  def cancelled?
    state == CANCELLED
  end

  def debit?
    manual_amount.nil?
  end

  def amount_for_fakir
    if manual_amount
      manual_amount
    elsif clinical_transfers.exists?
      clinical_transfers.not_cancelled.sum(:amount)
    else
      change_with_balance = changesets.select { |c| c.has_key?('saldo') }.last&.values_at('saldo')&.last
      change_with_balance&.first&.present? ? change_with_balance&.first&.abs : change_with_balance&.last&.abs
    end
  end

  def cro_currency
    cro&.currency
  end

  def currency_exchange
    currency_exchanges.last
  end

  def original_amount
    currency_exchange&.amount || manual_amount || clinical_transfers.not_cancelled.sum(:amount)
  end

  def final_amount
    currency_exchange&.amount || balance
  end

  def balance
    return original_amount if credit? && credit_paid_at.nil?
    saldo.abs
  end

  def needs_currency_exchange?
    note_currency != project&.currency
  end

  def note_currency
    contract_research_organization.currency
  end

  def project_currency
    project&.currency
  end

  def ever_credited?
    credited_at || credit? || credit_paid_at
  end

  def paid_without_credit?
    normal? && paid? && credit_paid_at.nil?
  end

  def currency
    read_attribute(:currency) || project.try(:currency)
  end

  def patients_with_transfers
    ClinicalUser.where(id: clinical_users.with_transfers_in_summary(self).pluck(:id).uniq)
  end

  def approved_by_researchers
    project_debit_signatures.signed.map(&:signed_by_researcher)
  end

  def balance_on_date(end_date:)
    ClinicalTransfer.where('project_debit_summary_id = ? AND created_at <= ?', id, end_date).exists? ? 0 : original_amount
  end

  def credit?
    note_type == CREDIT
  end

  def normal?
    note_type == NORMAL
  end

  def created?
    state == CREATED
  end

  def paid?
    state == PAID
  end

  def not_paid?
    state == CREATED
  end

  def all_clinical_transfers
    (clinical_transfers + direct_clinical_transfers + topup_transfers).flatten.uniq
  end

  def self.with_pir_account_number_and_amount(pir_acc_nr, amount)
    projects = Project.all.select { |p| p.get_active_account_number == pir_acc_nr }
    pds = projects.map(&:project_debit_summaries)
    pds << ProjectDebitSummary.where(project_id: projects.map(&:id))
    pds.flatten.select { |pds| (pds.saldo == (0 - amount.to_d)) && ![PAID, CANCELLED].include?(pds.state) }
  end

  def related_visits
    Visit.joins(:clinical_transfers).where(clinical_transfers: { id: clinical_transfers })
  end

  def exchange_or_cro_currency
    currency_exchange&.base_currency || cro_currency
  end

  def transfer_to_account_nr
    currency = exchange_or_cro_currency
    acc = manual_note_currency_account || cro.currency_accounts.where(currency: currency).first
    nr = acc.account_number

    "PL#{ ClinicalTransfer.format_account_number(nr) }"
  end

  def project
    project_id ? Project.find(project_id) : project_debits.first.try(:project)
  end

  def cro
    contract_research_organization
  end

  def email
    contract_research_organization.try(:researcher_for_pds_email).try(:email) || '<EMAIL>'
  end

  def amount
    project_debits
  end

  def balance_different_to_debit_balance?
    return false unless project_debits.any?

    original_amount != project_debits.map(&:amount_for_summary).sum
  end

  def set_debit_note_number
    SetDebitNoteNumber.new(pds: self).call
  end

  def incoming_transfers
    in_tr = ClinicalTransfer.unscoped.where(flow_direction: ClinicalTransfer::INCOMING)
                            .where(state: [ClinicalTransfer::BOOKED, ClinicalTransfer::SENT_TO_BANK])
                            .where('project_debit_summary_id = ? OR project_debit_id in (?)', id, pd_ids)
                            .where('status_change_date < ?', Time.now)
                            .order('status_change_date ASC')
  end

  def outgoing_transfers(states: [ClinicalTransfer::BOOKED, ClinicalTransfer::SENT_TO_BANK])
    out_tr = ClinicalTransfer.unscoped.where(flow_direction: ClinicalTransfer::OUTGOING)
                             .where('project_debit_summary_id = ? OR project_debit_id in (?)', id, pd_ids)
                             .where('status_change_date < ?', Time.now)
                             .order('status_change_date ASC')

    out_tr = out_tr.where(state: states) if states.present?
    out_tr
  end

  def pd_ids
    project_debits.map(&:id)
  end

  # obliczone saldo na podsatwie przelewów
  def saldo_computed(date = Time.now)
    pd_ids = project_debits.map(&:id)
    in_tr = ClinicalTransfer.unscoped.where(flow_direction: ClinicalTransfer::INCOMING)
                            .where(state: [ClinicalTransfer::BOOKED, ClinicalTransfer::SENT_TO_BANK])
                            .where('project_debit_summary_id = ? OR project_debit_id in (?)', id, pd_ids)
                            .where('status_change_date < ?', date)
                            .order('status_change_date ASC').sum(:amount)

    out_tr = ClinicalTransfer.unscoped.where(flow_direction: ClinicalTransfer::OUTGOING)
                             .where(state: [ClinicalTransfer::BOOKED, ClinicalTransfer::SENT_TO_BANK])
                             .where('project_debit_summary_id = ? OR project_debit_id in (?)', id, pd_ids)
                             .where('status_change_date < ?', date)
                             .order('status_change_date ASC').sum(:amount)

    in_tr - out_tr
  end

  def allowed_money
    project_debits.map(&:allowed_money).inject(0) { |sum, n| sum + n }
  end

  def knf_report_data(from = Date.today - 1.month, to = Date.today)
    from = from.beginning_of_day
    to = to.end_of_day

    pd_ids = project_debits.map(&:id)
    in_tr = ClinicalTransfer.unscoped.where(flow_direction: ClinicalTransfer::INCOMING)
                            .where(state: [ClinicalTransfer::BOOKED, ClinicalTransfer::SENT_TO_BANK])
                            .where('project_debit_summary_id = ? OR project_debit_id in (?)', id, pd_ids)
                            .where('status_change_date between ? and ?', from, to)
                            .where('amount != 0')
                            .order('status_change_date ASC')

    out_tr = ClinicalTransfer.unscoped.where(flow_direction: ClinicalTransfer::OUTGOING)
                             .where(state: [ClinicalTransfer::BOOKED, ClinicalTransfer::SENT_TO_BANK])
                             .where('project_debit_summary_id = ? OR project_debit_id in (?)', id, pd_ids)
                             .where('status_change_date between ? and ?', from, to)
                             .where('amount != 0')
                             .order('status_change_date ASC')

    sum_in = 0
    sum_in = in_tr.map(&:amount).inject(0) { |sum_in, n| sum_in + n }
    in_count = in_tr.size

    sum_out = 0
    sum_out = out_tr.map(&:amount).inject(0) { |sum_out, n| sum_out + n }
    out_count = out_tr.size

    [sum_in, in_count, sum_out, out_count]
  end

  # wybiera project_debits z danego project, ktore nadają sie do zestawienia:
  # - kazdy monitor złożył podpis potwierdzając swoje przelewy
  # - nie były włączone do wczesniejszej noty obciążeniowej
  # - nie są opłacone
  def self.find_ready_project_debits(projects)
    project_debits = ProjectDebit.where(project_id: Array(projects).map(&:id),
                                        status: ProjectDebit::SIGNED,
                                        project_debit_summary_id: nil)
                                 .order(:created_at)
  end

  def gen_new_xls
    xls = ProjectDebitXls.new(locale: locale, project_debit_ids: project_debits.map(&:id), sheet_name: note_number)
    xls.generate
    xls
  end

  def update_related_clinical_transfers_saldo_after_status_changed
    pd_ids = project_debits.map(&:id)

    ct = ClinicalTransfer.unscoped
                         .where(state: [ClinicalTransfer::BOOKED, ClinicalTransfer::SENT_TO_BANK])
                         .where('project_debit_summary_id = ? OR project_debit_id in (?)', id, pd_ids)
                         .order(:status_change_date)

    if ct.any?
      saldo = 0
      ct.each do |transfer|
        if transfer.flow_direction == ClinicalTransfer::INCOMING
          saldo += transfer.amount
        elsif transfer.flow_direction == ClinicalTransfer::OUTGOING
          saldo -= transfer.amount
        end
        transfer.saldo_after_status_changed = saldo
        transfer.save!(validate: false)
      end
    end
  end

  def generate_debit_note(project_debits, locales: %w[en pl], generate_in_foreign_currency: false)
    locales.each do |locale|
      ProjectDebitSummaries::GeneratePdf.new(
        project_debit_summary: self,
        project_debits: project_debits,
        locale: locale,
        generate_in_foreign_currency: generate_in_foreign_currency
      ).call
    end
  end

  def not_pl_currency?
    return false if currency.nil?

    currency != 'PLN'
  end

  def self.ready_foreign_currency_project_debits
    project_debits = []
    Project.not_test.each do |project|
      project_debits << ProjectDebitSummary.find_ready_project_debits(project)
    end
    project_debits.flatten!
  end

  def self.ready_pln_project_debits
    ProjectDebitSummary.find_ready_project_debits(Project.not_test.pln_currency)
  end

  def self.ready_foreign_currency_projects
    Project.quintiles_invoice.not_test.not_pln_currency.reject { |p| ProjectDebitSummary.find_ready_project_debits(p).empty? }.uniq
  end

  def send_to_managers
    transaction do
      self.state_changed_at = Time.new
      self.sent_at = Time.new
      save!
    end
    ResearcherMailer.with(project_debit_summary: self).notify_managers_about_ready_project_debit_summary.deliver
  end

  def get_or_create_pdf_created_at
    if pdf_created_at
      pdf_created_at
    else
      self.pdf_created_at = Time.now
      save
      pdf_created_at
    end
  end

  def render_prepaid_pdf(locale:, file_name:)
    PrepaidPdsPdf.new(pds: self, locale: locale).render_file file_name
  end

  def generate_prepaid_note_pdf
    ['pl', 'en'].each do |locale|
      file_name = "public/system/debit_pdfs/#{note_number.tr(' ', '_')}_#{locale}.pdf"
      render_prepaid_pdf(locale: locale, file_name: file_name)

      new_path = file_name.gsub!(%r{public/}, '')
      send("#{locale}_file_path=", new_path)

      if self.locale == locale
        self.file_path = new_path
      end

      self.save!
    end
  end

  def uniq_note_number_chunk
    split_nr = note_number.split('-')
    nr = split_nr[1]
    month_and_year = split_nr[2]

    if (note_number != note_number_was) && ProjectDebitSummary.where('note_number LIKE ?', "___#{nr}_#{month_and_year}%").any?
      errors.add(:note_number, 'numer noty z takim numerem porządkowym i datą już istnieje')
    end
  end

  def state_human_name(state_symbol: state)
    I18n.t("activerecord.attributes.project_debit_summary.states.#{state_symbol}")
  end

  # def note_type
  #   self.manual_amount ? 'Credit' : 'Debit'
  # end

  def cancel
    ProjectDebitSummary.transaction do
      project_debits.update_all(project_debit_summary_id: nil)
      self.state = CANCELLED
      save!
    end
  end

  def get_comment_for_pdf
    if cro.po_required_for_post_paid_note
      return
    end

    return comment if comment.present?
    return project.note_po if project.try(:note_po).present?

    if contract_research_organization.try(:note_po).present?
      contract_research_organization.note_po
    end
  end

  def file_name
    File.basename(file_path)
  end

  def regenerate
    if project.debit_allowed
      ProjectDebitsCron.regenerate_gen_quintiles_summary_debit_note(project, project_debit_summary_id: id, enforce_pl_currency: false)
    else
      generate_prepaid_note_pdf
    end
  end

  def state_color
    if paid_but_transfers_not_sent?
      return 'red'
    elsif credit?
      return 'blue'
    end

    case state
    when CREATED
      sent_at ? 'black' : 'red'
    when PAID
      'green'
    else
      'black'
    end
  end

  def paid_but_transfers_not_sent?
    paid? && unsent_transfers.exists?
  end

  def unpaid_patients
    unsent_transfers.map(&:clinical_user).uniq.compact
  end

  def unpaid_transfers_for_patient(clinical_user)
    unsent_transfers.where(clinical_user_id: clinical_user.id)
  end

  def unsent_transfers
    clinical_transfers.not_sent.not_grouped
  end

  def overdue?
    (created_at < 1.month.ago) && !paid?
  end

  def fee_plan
    project.fee_plan
  end

  def priority_details
    priority_name = fee_plan.notes_priority

    "Priority: #{priority_name} (#{project.fee_plan_name} Service Level)"
  end

  def credit_by_researcher(researcher:)
    update(note_type: ProjectDebitSummary::CREDIT, saldo: 0, credited_by_researcher_id: researcher.id)
  end

  def credit!
    update!(note_type: ProjectDebitSummary::CREDIT, saldo: 0)
  end

  def create_visit_actions
    clinical_transfers.each do |ct|
      create_visit_action(ct)
    end
  end

  def create_visit_action(clinical_transfer)
    cu = clinical_transfer.clinical_user
    if cu
      td = VisitAction.transfer_destination(cu)
      cu_conf = td == 'lmp' ? 'unconfirmed' : 'confirmed'
      va_name = "paid_note_cu_#{cu_conf}_paid_to_#{td}"
      if va_name
        GenerateVisitActionsJob.perform_later(visits: [clinical_transfer.visit], action_name: va_name)
      end
    end
  end

  def credited_at
    return nil unless credit?

    read_attribute(:credited_at) || ClinicalTransfer.where(project_debit_summary_id: id, flow_direction: ClinicalTransfer::INCOMING, state: ClinicalTransfer::BOOKED).order('id desc').first.try(:created_at)
  end

  def create_currency_account
    CurrencyAccount::ForNote.create!(
      currency: currency_used,
      resource: self
    )
  end

  private

  def set_paid_at
    self.paid_at = Time.current
    save(validate: false)
  end
end
