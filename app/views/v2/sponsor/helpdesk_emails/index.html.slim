scss:
  .select2-container {
    width: 300px !important;
  }
  .container {
    margin-left: 0 !important;
  }

.row.section_header
  .col-md-8.col-sm-10.col-xs-12
    = section_header(title: "Helpdesk", subtitle: 'Incoming requests')
    .mt-0
    = link_to 'New and pending', { scope: 'new_or_pending_or_suspended'}, class: "default_sm_btn #{'active' if ((@scope == 'new_or_pending_or_suspended') || @scope.nil?)}"
    = link_to 'Processed', { scope: 'processed'}, class: "default_sm_btn #{'active' if @scope == 'processed'}"
    = link_to 'Trash', { scope: 'removed'}, class: "default_sm_btn #{'active' if @scope == 'removed'}"
    = link_to 'All', { scope: 'all'}, class: "default_sm_btn #{'active' if @scope == 'all'}"
    = link_to 'Tasks', v2_sponsor_payclinical_employees_tasks_path, class: "default_sm_btn"

.tab-content.vertical_1.mt-1
  #panel1.tab-pane.fade.in.active role="tabpanel"
    .container style='width: auto;'
      = link_to "My requests (#{ @my_emails_count })", v2_sponsor_helpdesk_emails_path(scope: params[:scope], my_emails: true), class: 'default_sm_btn'
      = link_to "All requests (#{ @emails_count })", v2_sponsor_helpdesk_emails_path(scope: params[:scope]), class: 'default_sm_btn'
      = link_to "Filters", 'javascript:;', class: 'default_sm_btn', onclick: "$('#he__filters').toggle()"
      = link_to "Add a new request", new_v2_sponsor_operator_helpdesk_email_path, class: 'default_sm_btn'

      #he__filters.d-none
        = form_tag '', method: :get do
          .row
            .mt-1.col-xs-12
              = select_tag 'q[researcher_id_eq]', options_from_collection_for_select(@senders, :id, :email, params[:q].present? ? params[:q][:researcher_id_eq] : nil), include_blank: 'Sender', class: 'select2'
              br
              = text_field_tag 'q[from_cont]', params[:q].present? ? params[:q][:from_cont] : nil, placeholder: 'Email'
              br
              br
              = select_tag 'q[contract_research_organization_id_eq]', options_from_collection_for_select(@cros, :id, :name, params[:q].present? ? params[:q][:contract_research_organization_id_eq] : nil), include_blank: 'Sponsor', class: 'select2'
              br
              br
              = select_tag 'q[project_id_eq]', options_from_collection_for_select(@projects, :id, :clinical_protocol_code, params[:q].present? ? params[:q][:project_id_eq] : nil), include_blank: 'Project', class: 'select2'
              br
              br
              = select_tag 'q[operator_id_eq]', options_from_collection_for_select(@operators, :id, :name_and_email, params[:q].present? ? params[:q][:operator_id_eq] : nil), include_blank: 'Owner', class: 'select2'
              br
              br
              = select_tag 'q[request_type_eq]', options_from_collection_for_select(HelpdeskEmail.request_type.values, :to_s, :to_s, params[:q].present? ? params[:q][:request_type_eq] : nil), include_blank: 'Request type', class: 'select2'
              br
              br
              = select_tag 'q[status_eq]', options_from_collection_for_select(HelpdeskEmail.status.values, :to_s, :capitalize, params[:q].present? ? params[:q][:status_eq] : nil), include_blank: 'Status', class: 'select2'
              br
              br
              = text_field_tag 'q[receipt_id_cont]', params[:q].present? ? params[:q][:receipt_id_cont] : nil, placeholder: 'Request ID'
              br
              br
              = text_field_tag 'q[message_date_gteq]', params[:q].present? ? params[:q][:message_date_gteq] : nil, placeholder: 'Date from', class: 'datepicker'
              br
              = text_field_tag 'q[message_date_lteq]', params[:q].present? ? params[:q][:message_date_lteq] : nil, placeholder: 'Date to', class: 'datepicker'
              br
              = text_field_tag 'q[subject_cont]', params[:q].present? ? params[:q][:subject_cont] : nil, placeholder: 'Subject'
              br
              br
              = text_field_tag 'q[body_cont]', params[:q].present? ? params[:q][:body_cont] : nil, placeholder: 'Body'
              br
              br
              = text_field_tag 'q[resolved_at_gteq]', params[:q].present? ? params[:q][:resolved_at_gteq] : nil, placeholder: 'Resolved from', class: 'datepicker'
              br
              = text_field_tag 'q[resolved_at_lteq]', params[:q].present? ? params[:q][:resolved_at_lteq] : nil, placeholder: 'Resolved to', class: 'datepicker'
              br
              = text_field_tag 'q[created_at_gteq]', params[:q].present? ? params[:q][:created_at_gteq] : nil, placeholder: 'Created at from', class: 'datepicker'
              br
              = text_field_tag 'q[created_at_lteq]', params[:q].present? ? params[:q][:created_at_lteq] : nil, placeholder: 'Created at to', class: 'datepicker'
              br

          = submit_tag 'Search', class: 'submit_btn mt-1'

      .summary_section.default.mb-1.mt-1
        | We have received #{ HelpdeskEmail.created_today.count } requests today. Requests resolved today: #{ HelpdeskEmail.where(resolved_at: Time.current.all_day).count }.
      - if @emails.any?
        table.table
          thead
            th style="min-width: 130px" = sort_link(@q, :created_at, 'Date', default_order: :desc)
            th ID#/Att#
            th.text-xs-left = sort_link(@q, :from, 'From')
            th = sort_link(@q, :subject, 'Subject')
            th.text-xs-center Sponsor/Study
            th.text-xs-center = sort_link(@q, :status, 'Status')
            th.text-xs-center Owner
            th.fit_to_content
          tbody
            - @emails.each do |m|
              tr id="he_#{m.id}"
                td.relative
                  = with_he_colors(m, two_line_date_time(m.message_date, small_second_line: true))
                td.email__id_star.text-xs-center
                  span = link_to "#{ format('%05i', m.id) }/", v2_sponsor_helpdesk_email_path(m)
                  span class="#{ 'red' if m.helpdesk_email_attachments.size > 0 }" = m.helpdesk_email_attachments.size
                  br
                  span.row_second_line_2
                    = helpdesk_email_high_priority_icon(email: m)
                = render(HelpdeskEmails::Table::Td::FromComponent.new(helpdesk_email: m, params: params))
                = render(HelpdeskEmails::Table::Td::SubjectComponent.new(helpdesk_email: m))
                td = render(SponsorStudyComponent.new(cro: m.contract_research_organization, project: m.project))
                = render(HelpdeskEmails::Table::Td::StatusComponent.new(helpdesk_email: m))
                = render(HelpdeskEmails::Table::Td::AssignedComponent.new(helpdesk_email: m, researcher: current_researcher))
                = render(HelpdeskEmails::Table::Td::OptionsComponent.new(helpdesk_email: m, researcher: current_researcher))

        = paginate @emails
