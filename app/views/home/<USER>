scss:
  #service_levels_table {
    color: #313131;
    .fa {
      margin: 0;

      &.fa-check {
        color: #03b516;
      }

      &.fa-times {
        color: #e02361;
      }

    }
  }

  .pricing_calculator_form_optional_cost_entry .md-form {
    padding-top: 0 !important;
  }

section.section.section-lg.pb-0
  .container.container-short
    .row.row-grid.align-items-center
      .col-md-12.order-md-1
        .pr-md-5
          h2.display-4 Basic Costs
          p.text-justify.mb-5
            | Here is a juxtaposition of basic costs

          = render "basic_options"


section.section.section-lg.pb-4.py-lg
  .container.container-short
    .row.row-grid.align-items-center
      .col-md-12.order-md-1
        .pr-md-5
          h2.display-4 Calculator of Service Fees
          p.text-justify.mb-5
            | Do you have any questions about our references and success stories? Or would you like to become a customer?
            br
            | Our team of experts is ready and waiting to assist you. Please contact us and use us as a resource towards your business success.

          = render "service_levels"


/ section.section.section-lg.pb-4.py-lg.bg-lighter
  .container
    .row.row-grid.align-items-center
      .col-md-12.order-md-1
        .pr-md-5
          h2.display-4 Calculator
          p.text-justify.lh-130
            | Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

          div

            = simple_form_for @form, url: pricing_calculator_result_home_path, method: :post, remote: true, defaults: {wrapper: :vertical_md_form} do |f|
              p Do you have any questions about our references and success stories? Or would you like to become a customer?


              = f.input :number_of_patients
              = f.input :number_of_visits_per_patient
              = f.input :months

              .form-group.mt-2.mb-4
                label Service level
                = f.input_field :service_level, collection: ["Bronze", "Silver", "Gold", "Platinium"], class: '', style: 'width: 100%', include_blank: false

              = f.input :optional_cost_entry, as: :boolean, wrapper: :vertical_form, input_html: { class: 'filled-in form-check-input' }, label: "Oprional cost entry"


              p.mt-3 Our team of experts is ready and waiting to assist you. Please contact us and use us as a resource towards your business success.
              = f.submit "Calculate", class: 'btn btn-success mt-2 mb-3', data: { behavior: 'spinner_loader' }

              #pricing_table_container
                - if @calculation
                  = render "pricing_table", calculation: @calculation
