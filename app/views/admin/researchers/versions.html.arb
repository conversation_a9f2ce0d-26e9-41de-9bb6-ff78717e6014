panel '<PERSON>miany' do
  table_for PaperTrail::Version.where('(item_type = ? AND item_id = ?) OR (item_type = ? AND item_id in (?)) OR (item_type = ? AND item_id in (?))', 'Researcher', resource.id, 'ProjectRole', resource.project_roles.with_deleted.pluck(:id), 'ClinicalCenterRole', resource.clinical_center_roles.pluck(:id)).order('created_at desc').includes([:item]) do |t|
    t.column('Zmieniony przez') { |r| link_to_changer(changer: r.whodunnit) }
    t.column('Data') { |r| r.created_at.to_s }
    t.column('<PERSON><PERSON><PERSON>') do |r|
      if r.changeset.present?
        r.changeset
      elsif r.event == 'destroy'
        "Usunięto #{r.item_type} #{r.item_id}"
      end
    end
  end
end