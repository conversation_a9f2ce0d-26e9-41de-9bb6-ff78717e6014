require 'application_system_test_case'

class V2::Sponsor::Projects::ResearchersTest < ApplicationSystemTestCase
  setup do
    @project = projects(:project)
  end

  test 'view researchers in different sites' do
    clinical_center = clinical_centers(:clinical_center)
    closed_cc = clinical_centers(:closed_clinical_center)
    closed_cc.update_columns(closed: false)
    researcher = researchers(:jack)
    sign_in researcher

    visit v2_sponsor_project_researchers_path(@project)

    assert_text 'Site 123'
    assert_text 'Jax Mad'
    assert_text 'Site 456'
    assert_text 'Jack Black'

    find("[data-behavior='slide_up_trigger'][data-target='.cc_#{clinical_center.id}_tr']").click
    assert_no_text 'Jack Black'

    find("[data-behavior='slide_up_trigger'][data-target='.cc_#{closed_cc.id}_tr']").click
    assert_text 'Jack Black'
    assert_no_text 'Jax Mad'

    @project.update_columns(test_project: true)
    visit root_path

    visit v2_sponsor_project_researchers_path(@project)

    assert_text 'Site 123'
    assert_no_text '<PERSON>'
    assert_text 'Site 456'
    assert_text 'Jack <PERSON>'

    find("[data-behavior='slide_up_trigger'][data-target='.cc_#{clinical_center.id}_tr']").click
    assert_no_text 'Jack Black'

    find("[data-behavior='slide_up_trigger'][data-target='.cc_#{closed_cc.id}_tr']").click
    assert_text 'Jack Black'
    assert_no_text 'Jax Mad'
  end
end
