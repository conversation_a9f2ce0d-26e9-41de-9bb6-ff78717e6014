require 'application_system_test_case'

module Admin
  module Projects
    class RegenerateFeesTest < NoJsSystemTestCase
      test 'regenerate fees' do
        project = projects(:project)
        sign_in admin_users(:admin)
        visit admin_project_path(project)
        click_link 'Zregeneruj opłaty'

        expected_date = 1.month.ago.beginning_of_month
        ::Projects::RegenerateFeesJob.expects(:perform_later).with do |args|
          args[:project].id == project.id &&
            (args[:date].to_i - expected_date.to_i).abs < 86400 # Within 1 day
        end

        click_button 'Zregeneruj'
      end
    end
  end
end