require 'test_helper'

class HelpdeskEmailsHelperTest < ActionView::TestCase
  setup do
    @email = helpdesk_emails(:new_helpdesk_email)
  end

  test '#formatted_helpdesk_email_body - squashes multiple new lines into one' do
    body = "foo\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nbar"
    result = formatted_helpdesk_email_body(body)

    assert_equal 'foo<br/><br/>bar', result
  end

  test '#new_helpdesk_email_link' do
    result = new_helpdesk_email_link

    assert_includes result, 'Helpdesk'
  end

  test '#with_he_colors - red text when email over response_time' do
    @email.created_at = 10.days.ago
    @email.status = 'pending'

    assert_equal '<span class="red">foo</span>', with_he_colors(@email, 'foo')
  end

  test '#with_he_colors - return just the body' do
    @email.created_at = 10.minutes.ago

    assert_equal 'foo', with_he_colors(@email, 'foo')
  end

  test '#helpdesk_email_high_priority_icon' do
    @email.researcher = researchers(:researcher)

    assert_equal 2, helpdesk_email_high_priority_icon(email: @email).scan(/fa-star/).count

    CroRole.delete_all

    assert_equal 1, helpdesk_email_high_priority_icon(email: @email).scan(/fa-star/).count

    @email.vip = true

    assert_equal 3, helpdesk_email_high_priority_icon(email: @email).scan(/fa-star/).count

    @email.vip = false
    @email.researcher.project_roles.managers.update_all(project_role: 'CRA')

    assert_equal 0, helpdesk_email_high_priority_icon(email: @email).scan(/fa-star/).count
  end
end
