require 'test_helper'

class HelpdeskEmailTest < ActiveSupport::TestCase
  setup do
    @site_setting = site_settings(:site_setting)
  end

  test "from_premium_projects" do
    result = HelpdeskEmail.from_premium_projects

    assert_includes result, helpdesk_emails(:premium_pending_other_helpdesk_email)
    assert_includes result, helpdesk_emails(:premium_cro_helpdesk_email)
    assert_not_includes result, helpdesk_emails(:new_helpdesk_email)
  end

  test '#response_template_name' do
    he = helpdesk_emails(:new_helpdesk_email_with_researcher)
    researcher = he.researcher
    ClinicalCenterRole.delete_all

    ProjectRole.delete_all
    ProjectRole.new(researcher: researcher, project: he.project, project_role: 'Manager').save!(validate: false)
    assert_equal :manager, he.response_template_name

    ClinicalCenterRole.delete_all
    ProjectRole.delete_all
    ProjectRole.new(researcher: researcher, project: he.project, project_role: 'CRA').save!(validate: false)
    assert_equal :cra, he.response_template_name

    ProjectRole.delete_all
    ProjectRole.new(researcher: researcher, project: he.project, project_role: 'CRA+').save!(validate: false)
    assert_equal :cra, he.response_template_name

    ProjectRole.delete_all
    ProjectRole.new(researcher: researcher, project: he.project, project_role: 'Investigator').save!(validate: false)
    assert_equal :investigator, he.response_template_name

    ProjectRole.delete_all
    ProjectRole.new(researcher: researcher, project: he.project, project_role: 'Investigator+').save!(validate: false)
    assert_equal :investigator, he.response_template_name

    he.researcher = nil
    assert_equal :not_registered, he.response_template_name
  end

  ['Manager', 'CRA', 'Investigator'].each do |role|
    test "autoresponse with template #{role}" do
      template_text = 'template text {{receipt_id}}'
      @site_setting.update!(
        "helpdesk_email_#{role.downcase}_response_email_subject": 'subject',
        "helpdesk_email_#{role.downcase}_response_email_body": template_text,
        "helpdesk_email_#{role.downcase}_response_sms_body": template_text,
      )

      he = helpdesk_emails(:new_helpdesk_email_with_researcher)
      ClinicalCenterRole.delete_all
      ProjectRole.delete_all
      ProjectRole.new(researcher: he.researcher, project: he.project, project_role: role).save!(validate: false)

      assert_difference 'ActionMailer::Base.deliveries.size' do
        assert_difference 'ClinicalMobileMessage.count' do
          he.autorespond

          email = ActionMailer::Base.deliveries.last
          assert_includes email.body.to_s, "template text #{he.receipt_id}"
          assert_equal email.subject, 'subject'

          sms = ClinicalMobileMessage.last
          assert_includes sms.msg, "template text #{he.receipt_id}"
        end
      end
    end
  end

  test "autoresponse with template for not registered researcher" do
    template_text = 'template text {{receipt_id}}'
    @site_setting.update!(
      "helpdesk_email_not_registered_response_email_subject": 'subject {{receipt_id}}',
      "helpdesk_email_not_registered_response_email_body": template_text,
      "helpdesk_email_not_registered_response_sms_body": template_text,
      )

    he = helpdesk_emails(:new_helpdesk_email)

    assert_difference 'ActionMailer::Base.deliveries.size' do
      assert_no_difference 'ClinicalMobileMessage.count' do
        he.autorespond

        email = ActionMailer::Base.deliveries.last
        assert_includes email.body.to_s, "template text #{he.receipt_id}"
        assert_equal email.subject, "subject #{he.receipt_id}"
        assert_empty email.cc
      end
    end
  end

  test "autoresponse with template operator" do
    role = 'Operator'
    he = helpdesk_emails(:new_helpdesk_email_with_researcher)
    ClinicalCenterRole.delete_all
    ProjectRole.delete_all
    ProjectRole.new(researcher: he.researcher, project: he.project, project_role: role).save!(validate: false)

    assert_no_difference 'ActionMailer::Base.deliveries.size' do
      assert_no_difference 'ClinicalMobileMessage.count' do
        he.autorespond
      end
    end
  end

  test 'autoresponse with SiteSetting cc' do
    he = helpdesk_emails(:new_helpdesk_email)
    @site_setting.update!(helpdesk_email_response_cc: '<EMAIL>, <EMAIL>')

    assert_difference 'ActionMailer::Base.deliveries.size' do
      he.autorespond
    end

    email = ActionMailer::Base.deliveries.last
    assert_equal email.cc, ['<EMAIL>', '<EMAIL>']
  end

  test '#contains_personal_data_form - change request_type to Form' do
    email = helpdesk_emails(:new_helpdesk_email)

    assert_changes 'email.request_type', to: 'Form' do
      email.contains_personal_data_form = true
      email.save!
    end
  end

  test '#contains_personal_data_form - no change if false' do
    email = helpdesk_emails(:new_helpdesk_email)

    assert_no_changes 'email.request_type' do
      email.contains_personal_data_form = false
      email.save!
    end
  end

  test '#send_task_category_notifications - status new to pending' do
    task_category = helpdesk_emails_task_categories(:forms)
    he = helpdesk_emails(:new_helpdesk_email_with_researcher)
    he.task_category = task_category

    assert_no_difference 'ClinicalMobileMessage.count' do
      assert_no_emails do
        he.save!
      end
    end

    task_category.pending_email_subject = 'subject'
    task_category.pending_email_body = 'body'
    task_category.pending_sms_body = 'SMS body'
    he.status = 'pending'

    assert_difference 'ClinicalMobileMessage.count' do
      assert_emails 1 do
        he.save!
      end
    end

    task_category.pending_sms_body = nil

    assert_no_difference 'ClinicalMobileMessage.count' do
      assert_emails 1 do
        he.status = 'new'
        he.save!
        he.status = 'pending'
        he.save!
      end
    end

    task_category.pending_email_subject = nil
    task_category.pending_email_body = nil
    task_category.pending_sms_body = 'SMS body'

    assert_difference 'ClinicalMobileMessage.count' do
      assert_no_emails do
        he.status = 'new'
        he.save!
        he.status = 'pending'
        he.save!
      end
    end
  end

  test '#send_task_category_notifications - status to suspended' do
    task_category = helpdesk_emails_task_categories(:forms)
    he = helpdesk_emails(:new_helpdesk_email_with_researcher)
    he.task_category = task_category

    assert_no_difference 'ClinicalMobileMessage.count' do
      assert_no_emails do
        he.save!
      end
    end

    task_category.suspended_email_subject = 'subject'
    task_category.suspended_email_body = 'body'
    task_category.suspended_sms_body = 'SMS body'
    he.status = 'suspended'

    assert_difference 'ClinicalMobileMessage.count' do
      assert_emails 1 do
        he.save!
      end
    end
  end

  test '#send_task_category_notifications - status from suspended to pending' do
    task_category = helpdesk_emails_task_categories(:forms)
    he = helpdesk_emails(:new_helpdesk_email_with_researcher)
    he.task_category = task_category

    assert_no_difference 'ClinicalMobileMessage.count' do
      assert_no_emails do
        he.save!
      end
    end

    task_category.reactivated_email_subject = 'subject'
    task_category.reactivated_email_body = 'body'
    task_category.reactivated_sms_body = 'SMS body'
    he.status = 'suspended'
    he.save!
    he.status = 'pending'

    assert_difference 'ClinicalMobileMessage.count' do
      assert_emails 1 do
        he.save!
      end
    end
  end

  test '#send_task_category_notifications - status resolved' do
    task_category = helpdesk_emails_task_categories(:forms)
    he = helpdesk_emails(:new_helpdesk_email_with_researcher)
    he.task_category = task_category

    assert_no_difference 'ClinicalMobileMessage.count' do
      assert_no_emails do
        he.save!
      end
    end

    task_category.resolved_email_subject = 'subject'
    task_category.resolved_email_body = 'body'
    task_category.resolved_sms_body = 'SMS body'
    he.status = 'resolved'

    assert_difference 'ClinicalMobileMessage.count' do
      assert_emails 1 do
        he.save!
      end
    end
  end

  test '#send_task_category_notifications - status closed' do
    task_category = helpdesk_emails_task_categories(:forms)
    he = helpdesk_emails(:new_helpdesk_email_with_researcher)
    he.task_category = task_category

    assert_no_difference 'ClinicalMobileMessage.count' do
      assert_no_emails do
        he.save!
      end
    end

    task_category.closed_email_subject = 'subject'
    task_category.closed_email_body = 'body'
    task_category.closed_sms_body = 'SMS body'
    he.status = 'closed'

    assert_difference 'ClinicalMobileMessage.count' do
      assert_emails 1 do
        he.save!
      end
    end
  end

  test '#send_task_category_notifications - with manual_status_change_response' do
    task_category = helpdesk_emails_task_categories(:forms)
    he = helpdesk_emails(:new_helpdesk_email_with_researcher)
    he.task_category = task_category

    assert_no_difference 'ClinicalMobileMessage.count' do
      assert_no_emails do
        he.save!
      end
    end

    task_category.closed_email_subject = 'subject {{manual_response}}'
    task_category.closed_email_body = 'body {{manual_response}}'
    task_category.closed_sms_body = 'SMS body {{manual_response}}'
    he.status = 'closed'
    he.manual_status_change_response = 'manual'

    assert_difference 'ClinicalMobileMessage.count' do
      assert_emails 1 do
        he.save!
      end
    end

    email = ActionMailer::Base.deliveries.last
    assert_includes email.body.to_s, 'manual'
    assert_includes email.subject, 'manual'

    sms = ClinicalMobileMessage.last
    assert_includes sms.msg, 'manual'
  end

  test '#unregistered_email?' do
    he = HelpdeskEmail.new(from: '<EMAIL>')
    assert he.unregistered_email?

    he.from = researchers(:researcher).email
    refute he.unregistered_email?

    cu = clinical_users(:clinical_user)
    cu.update_column :email, '<EMAIL>'
    he.from = cu.email
    refute he.unregistered_email?
  end

  test 'vip researcher HE auto upgrades to vip' do
    researcher = researchers(:researcher)
    researcher.update_columns(vip: false)

    he = helpdesk_emails(:new_helpdesk_email_with_researcher).dup
    he.researcher = researcher
    he.save!

    refute he.vip

    researcher.update_columns(vip: true)
    he = helpdesk_emails(:new_helpdesk_email)
    he.researcher = researcher
    he.save!

    assert he.reload.vip

    he = helpdesk_emails(:new_helpdesk_email_with_researcher).dup
    he.researcher = researcher
    he.save!

    assert he.vip
  end

  test '.new_or_pending_or_suspended' do
    suspended_he = helpdesk_emails(:response_helpdesk_email)
    suspended_he.update_columns(status: 'suspended')

    removed_he = helpdesk_emails(:checked_in_email)
    removed_he.update_columns(status: 'removed')

    result = HelpdeskEmail.new_or_pending_or_suspended
    assert_includes result, helpdesk_emails(:new_helpdesk_email)
    assert_includes result, helpdesk_emails(:premium_pending_other_helpdesk_email)
    assert_includes result, suspended_he
    assert_not_includes result, removed_he
  end
end
