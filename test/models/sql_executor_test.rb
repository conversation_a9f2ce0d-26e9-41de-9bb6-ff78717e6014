require 'test_helper'

class SqlExecutorTest < ActiveSupport::TestCase
  test 'execute select' do
    sql =  'SELECT clinical_protocol_code as study_protocol FROM "projects" WHERE "projects"."contract_research_organization_id" = {{cro_id}} LIMIT {{limit}}'
    params = "cro_id: #{contract_research_organizations(:cro).id}, limit: 3"

    result = SqlExecutor.new(sql, params).execute
    expected = [
      { 'study_protocol' => projects(:project).clinical_protocol_code },
      { 'study_protocol' => projects(:debit_project).clinical_protocol_code },
      { 'study_protocol' => projects(:cra_project).clinical_protocol_code }
    ]

    assert_equal expected, result.map { _1 }
  end

  test 'execute select with string' do
    sql =  'SELECT clinical_protocol_code as study_protocol FROM "projects" WHERE "projects"."name" = {{name}}'
    params = "name: #{projects(:project).name}"

    result = SqlExecutor.new(sql, params).execute
    expected = [
      { 'study_protocol' => projects(:project).clinical_protocol_code },
      { 'study_protocol' => projects(:cra_project).clinical_protocol_code },
    ]

    assert_equal expected, result.map { _1 }
  end

  test 'no updates' do
    sql =  'UPDATE "projects" SET clinical_protocol_code = :new_code WHERE "projects"."contract_research_organization_id" = {{cro_id}}'
    params = "cro_id: #{contract_research_organizations(:cro).id}, new_code: new_code"

    assert_raises ActiveRecord::StatementInvalid do
      SqlExecutor.new(sql, params).execute
    end
  end

  test 'no deletes' do
    sql =  'DELETE FROM "projects" WHERE "projects"."contract_research_organization_id" = {{cro_id}}'
    params = "cro_id: #{contract_research_organizations(:cro).id}"

    assert_raises ActiveRecord::StatementInvalid do
      SqlExecutor.new(sql, params).execute
    end
  end

  test 'no inserts' do
    sql =  'INSERT INTO "projects" (clinical_protocol_code, contract_research_organization_id) VALUES (:new_code, :cro_id)'
    params = "cro_id: #{contract_research_organizations(:cro).id}, new_code: new_code"

    assert_raises ActiveRecord::StatementInvalid do
      SqlExecutor.new(sql, params).execute
    end
  end

  test '#missing params' do
    sql =  'SELECT clinical_protocol_code as study_protocol FROM "projects" WHERE "projects"."contract_research_organization_id" = {{cro_id}} LIMIT {{limit}}'

    params = ""
    assert_equal ['cro_id', 'limit'], SqlExecutor.new(sql, params).missing_params

    params = "cro_id: 1"
    assert_equal ['limit'], SqlExecutor.new(sql, params).missing_params

    params = "cro_id: 1, limit: 2"
    assert_equal [], SqlExecutor.new(sql, params).missing_params
  end
end
