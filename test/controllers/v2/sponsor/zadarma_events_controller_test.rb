require 'test_helper'

module V2
  module Sponsor
    class ZadarmaEventsControllerTest < ActionDispatch::IntegrationTest
      test 'index' do
        sign_in researchers(:operator)

        get v2_sponsor_zadarma_events_path

        assert_response :success
        assert_includes assigns(:events), zadarma_events(:event_with_project)
        assert_not_includes assigns(:events), zadarma_events(:internal_event)
      end
    end
  end
end
