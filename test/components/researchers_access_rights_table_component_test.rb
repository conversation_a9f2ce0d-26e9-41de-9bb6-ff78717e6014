require "test_helper"

class ResearchersAccessRightsTableComponentTest < ViewComponent::TestCase
  setup do
    @researcher = researchers(:researcher)
  end

  test 'status column and dropdown' do
    render_inline(ResearchersAccessRightsTableComponent.new(researchers: Researcher.where(id: @researcher.id).page(nil)))
    assert_selector('span.text-success', text: 'active')

    @researcher.update_columns blocked_at: Time.new
    render_inline(ResearchersAccessRightsTableComponent.new(researchers: Researcher.where(id: @researcher.id).page(nil)))
    assert_selector('span.text-warning', text: 'locked')
    assert_selector('.dropdown-menu a:first-child', text: 'Unlock account')

    @researcher.update_columns blocked_at: nil, deactivated_after_inactive_at: Time.new
    render_inline(ResearchersAccessRightsTableComponent.new(researchers: Researcher.where(id: @researcher.id).page(nil)))
    assert_selector('span.text-danger', text: 'deactivated')
    assert_selector('.dropdown-menu a:first-child', text: 'Reactivate account')
  end
end
