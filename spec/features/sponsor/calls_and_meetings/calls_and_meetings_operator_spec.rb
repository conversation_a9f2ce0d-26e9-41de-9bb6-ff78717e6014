require "rails_helper"

RSpec.feature "Calls and meetings for operators (payclinical employees)", :type => :feature do
  let(:researcher) { create :researcher, payclinical_employee: true }
  let(:project) { create :project }
  let(:clinical_center) { create :clinical_center, project: project }
  let!(:project_role) { create :project_role, project_id: project.id, researcher_id: researcher.id, project_role: 'Operator' }

  scenario "#index - can access all calls and meetings", js: true do
    cm = create :calls_and_meeting
    cm2 = create :calls_and_meeting

    login(researcher)
    visit v2_sponsor_calls_and_meetings_path

    # All Calls/Meetings are listed
    expect(page).to have_css('.row_second_line_2', count: 2) # two datetimes, one for each call_meeting

    # CROs in the table are listed
    within('table th:nth-child(2)') do
      expect(page).to have_content('CRO') # CRO in table header
    end

    # Has access to dropdown actions
    within('tbody tr:first-child') do
      click_button('Select')
      expect(page).to have_content('Show')
      expect(page).to have_content('Edit')
      expect(page).to have_content('Set as done')
      expect(page).to have_content('Cancel')
      expect(page).to have_content('Remove')
    end
  end

  scenario "#edit", js: true do
    cm = create :calls_and_meeting

    login(researcher)
    visit v2_sponsor_calls_and_meetings_path

    within('tbody tr:first-child') do
      click_button('Select')
      click_link('Edit')
    end

    date = cm.date_and_time.to_date

    time = '12:30'
    fill_in "calls_and_meeting_time_part", with: time
    find('.clockpicker-hours .clockpicker-tick', text: '12').click
    sleep(0.1) # wait for javascript
    find('.clockpicker-minutes .clockpicker-tick', text: '30').click
    find('.picker__footer button').click

    # Fill trix editor before editing time input, as opened picker interferes with flow of a document
    agenda = 'New text'
    find('#calls_and_meeting_agenda ~ trix-editor').set(agenda)

    # select2 '<EMAIL>,<EMAIL>,invalid@email', from: 'calls_and_meeting_receivers', tag: true
    # fill_in "calls_and_meeting_receivers", with: '<EMAIL>,<EMAIL>,invalid@email'
    click_button 'Save'

    # saves changes correctly
    cm.reload
    # expect(cm.receivers).to eq("<EMAIL>, <EMAIL>")
    expect(cm.date_and_time.to_date).to eq(date)
    expect(cm.date_and_time.strftime('%H:%M')).to eq(time)
    expect(cm.agenda).to eq("<div>#{agenda}</div>")
  end

  scenario "change state", js: true do
    cm = create :calls_and_meeting

    login(researcher)
    visit v2_sponsor_calls_and_meetings_path

    within('tbody tr:first-child td:nth-child(7)') do
      expect(page).to have_content('Planned') # status: 'planned'
    end

    within('tbody tr:first-child') do
      click_button('Select')
      click_link('Set as done')
    end

    within('tbody tr:first-child td:nth-child(7)') do
      expect(page).to have_content('Completed')
    end
  end

  scenario "#destroy", js: true do
    create :calls_and_meeting
    create :calls_and_meeting

    login(researcher)
    visit v2_sponsor_calls_and_meetings_path

    within('tbody tr:first-child') do
      click_button('Select')
      sleep 1
      click_link('Remove')
    end

    within('body.modal-open') do
      click_button('Confirm')
    end

    expect(page).to have_css('.row_second_line_2', count: 1) # one datetime, for one remaining call_meeting
  end

  scenario "send agenda by email", js: true do
    agenda = 'This is an agenda'
    cm = create :calls_and_meeting, agenda: agenda

    login(researcher)
    visit v2_sponsor_calls_and_meetings_path

    within('tbody tr:first-child') do
      click_button('Select')
      click_link('Send agenda')
    end

    fill_in "send_call_meeting_email_form_main_receiver", with: '<EMAIL>'
    click_button 'Send'

    within('tbody tr:first-child td:nth-child(5)') do
      expect(page).to have_css('.row_second_line_2')
    end

    open_email('<EMAIL>')
    expect(current_email).to have_content(agenda)
  end

  scenario "send meeting minutes by email", js: true do
    meeting_minutes = 'This is minutes'
    cm = create :calls_and_meeting, meeting_minutes: meeting_minutes

    login(researcher)
    visit v2_sponsor_calls_and_meetings_path

    within('tbody tr:first-child') do
      click_button('Select')
      click_link('Send minutes')
    end

    fill_in "send_call_meeting_email_form_main_receiver", with: '<EMAIL>'
    click_button 'Send'

    within('tbody tr:first-child td:nth-child(6)') do
      expect(page).to have_css('.row_second_line_2')
    end

    open_email('<EMAIL>')
    expect(current_email).to have_content(meeting_minutes)
  end

  scenario "PDF with agenda/minutes" do
    cm = create :calls_and_meeting

    login(researcher)
    visit v2_sponsor_calls_and_meetings_path

    # Dropdown actions don't allow to download agenda/minutes PDF if there is no agenda/minutes
    within('tbody tr:first-child') do
      click_button('Select')
      expect(page).not_to have_content('Download agenda')
      expect(page).not_to have_content('Download minutes')
    end

    cm.update(agenda: 'Test agenda', meeting_minutes: 'Test minutes')
    cm.reload
    visit v2_sponsor_calls_and_meetings_path

    within('tbody tr:first-child') do
      click_button('Select')
      expect(page).to have_content('Download agenda')
      expect(page).to have_content('Download minutes')
      click_link('Download minutes')
    end

    expect(response_headers["Content-Type"]).to eq("application/pdf")
  end
end
