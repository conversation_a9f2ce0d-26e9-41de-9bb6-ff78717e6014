require 'rails_helper'

RSpec.describe Projects::CloneCurrency do
  let(:project) { create :project, currency: 'PLN' }
  let(:currency) { Currency::AVAILABLE.last }
  let(:service) { Projects::CloneCurrency.new(project: project, currency: currency) }

  describe 'call' do
    it 'creates new project' do
      expect { service.call }.to change { Project.count }.to(2)
    end

    it 'copies templates' do
      pvt = create :project_visit_template, project_id: project.id
      vt = create :visit_template, project_visit_template_id: pvt.id

      service.call

      expect(service.new_project.project_visit_templates.count).to eq 1
      expect(service.new_project.visit_templates.count).to eq 1
    end

    it 'copies visit_types' do
      vt = create :visit_type, project_id: project.id

      service.call

      expect(service.new_project.visit_types.count).to eq 1
    end

    it 'copies managers' do
      create :project_role, project_id: project.id, project_role: 'Manager'

      service.call

      expect(service.new_project.project_roles.count).to eq 1
    end

    it 'copies visit_payment_categories' do
      create :visit_payment_category, project_id: project.id

      service.call

      expect(service.new_project.visit_payment_categories.count).to eq 1
    end
  end
end
