source 'https://rubygems.org'
source 'http://rails-assets.org'

ruby '3.3.5'
gem 'rails', '8.0.2'
gem 'pg'
gem 'devise'
gem 'cancancan'

gem 'activeadmin'
gem 'just-datetime-picker'
gem 'ransack'
gem 'phantomjs'
gem 'brakeman'
gem 'simple_form'#, '>= 2.0.4'
gem 'kaminari'
gem 'unicode_utils'
gem 'descriptive-statistics'

gem 'savon'
gem 'rqrcode_png'

gem 'nokogiri'
gem 'carrierwave'
gem 'summernote-rails'
gem 'rubyzip', '>= 1.0.0' # will load new rubyzip version
gem 'zip-zip' # will load compatibility for old rubyzip API.

gem 'prawn', '~> 2.2'
gem 'prawn-table'
gem 'prawn-templates', '~> 0.1.1'
gem 'prawn-markup'
gem 'combine_pdf'
gem 'pdf-reader', '~> 2.14.1'

gem 'caxlsx_rails'
gem 'acts_as_xlsx'
gem 'mime-types'
# ponizszy 'niby' lepiej wspiera RVM
gem 'whenever'
gem 'spreadsheet'

gem 'jquery-rails'
gem 'jquery-ui-rails'

gem 'bootstrap-sass'
gem 'select2-rails', '3.4.1', github: 'SimonBo/select2-rails', branch: 'rails_7_431'
gem 'awesome_print'

gem 'certified'
gem 'time_difference'
gem 'gon'
gem 'font-awesome-rails', '~> 4.7'

gem 'chunky_png'
gem 'd3-rails'
gem 'groupdate'
gem 'enumerize'
gem 'tether-rails'
gem 'acts_as_list'
gem 'rails-assets-bootbox', '~> 4.4.0'
gem 'decent_exposure'
gem 'coffee-rails'
gem 'sass-rails'

group :test do
  gem "rails-controller-testing"
  gem 'database_cleaner'
  gem 'email_spec'
  gem 'launchy'
  gem 'capybara'
  gem 'pdf-inspector', :require => "pdf/inspector"
  gem 'selenium-webdriver'
  gem 'capybara-email'
  gem 'rspec-html-matchers'
  gem 'simplecov', require: false
  gem 'capybara-screenshot'
  # gem 'webdrivers'
  gem 'mocha'
  gem 'minitest-focus'
  gem 'capybara-select2'
  gem 'webmock'
  gem 'vcr'
  gem 'minitest-stub-const'
end

group :development do
  gem 'bullet'
  gem 'listen'
  gem 'web-console'
end

gem 'capistrano-rails'
gem 'capistrano-rvm', '~> 0.1.0'
gem 'capistrano-bundler'
gem "capistrano-db-tasks", require: false, git: 'https://github.com/SimonBo/capistrano-db-tasks.git'

group :test, :development do
  gem 'faker'
  gem 'rspec-rails'
  gem 'poltergeist'
  gem 'timecop'
  gem 'factory_bot_rails'
  gem 'puma'
  gem 'parallel_tests'
  gem 'bundler-audit', require: false
  gem 'rubocop', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-performance', require: false
  gem 'rubocop-rspec', require: false # If you're using RSpec
  gem 'rubocop-minitest', require: false # If you're using Minitest
  gem 'rubocop-rake', require: false
end

group :development, :production do
  gem 'rails_performance'
  gem 'unicorn', '>= 4.3.1'
end

gem 'rails-patch-json-encode'
gem 'oj'
gem 'exception_notification'
gem 'slim'
gem 'geocoder'
gem 'yaml_db'
gem 'gruff', '~> 0.6.0'
gem 'daemons'
gem 'chronic', '0.10.2'
gem 'active_admin_sidebar'
gem 'sitemap_generator'
gem 'meta-tags'
gem "paranoia"#, "~> 2.4.2"
gem "cocoon"
gem 'rmagick', require: false
gem 'data-confirm-modal'
gem 'email_validator'
gem 'carrierwave-base64'
gem 'sucker_punch'
gem 'pundit'
gem 'paper_trail'
gem 'devise-security'
gem 'unread'
gem 'browsernizer'
gem "avatarly"
gem 'jquery-timepicker-addon-rails'
gem 'active_record_query_trace'
gem 'rails-assets-remarkable-bootstrap-notify', source: 'http://rails-assets.org'
gem 'locales_export_import'
gem 'remotipart', '~> 1.2'
gem 'active_model_otp'
gem 'addressable'
gem 'aasm'
gem 'json'
gem 'dkim'

gem 'high_voltage'
gem 'paranoia_uniqueness_validator'
gem 'pluck_all'
gem 'rack-mini-profiler'
gem 'virtus'
gem 'bootsnap', require: false
gem 'sprockets-rails'
gem 'trix-rails', require: 'trix'
gem 'axlsx_styler'
gem 'icalendar'
gem 'httparty'
gem 'cmxl'
gem "view_component"

# for capistrano db to work
gem 'ed25519', '>= 1.2', '< 2.0'
gem 'bcrypt_pbkdf', '>= 1.0', '< 2.0'
gem 'pluck_to_hash'
gem 'mimemagic', github: 'mimemagicrb/mimemagic', ref: '01f92d86d15d85cfd0f20dabd025dcbd36a8a60f'
gem 'rfc822', github: 'andresprogra/rfc822'
gem 'redis-namespace'

gem 'rdoc', '6.14.0', require: false